{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% translate "Corporate Reservations" %}{% endblock %}

{% block content %}
<div class="container-fluid py-5">
    <!-- Corporate Details Section (if viewing a specific corporate) -->
    {% if corporate %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white py-3">
                    <h5 class="mb-0 font-weight-bold">{{ corporate.legal_name }} - {% translate "Reservations" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <p><strong>{% translate "Admin" %}:</strong> {{ corporate.corporate_admin.get_full_name }}</p>
                            <p><strong>{% translate "Email" %}:</strong> {{ corporate.corporate_admin.email }}</p>
                        </div>
                        <div class="col-md-4">
                            <p><strong>{% translate "Phone" %}:</strong> {{ corporate.phone_number }}</p>
                            <p><strong>{% translate "Category" %}:</strong> {{ corporate.get_category_display }}</p>
                        </div>
                        <div class="col-md-4">
                            <p><strong>{% translate "Capacity" %}:</strong> {{ corporate.capacity }}</p>
                            <a href="{% url 'website:corporate_admins_view' %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-arrow-left me-1"></i> {% translate "Back to Corporate Admins" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Course Requests Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-lg border-0 bg-warning bg-opacity-10">
                <div class="card-header bg-warning bg-opacity-25 py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 font-weight-bold d-flex align-items-center text-warning">
                        <i class="fas fa-file-invoice me-2"></i>
                        {% translate "Corporate Course Requests" %}
                        <span id="pending-requests-count" class="badge bg-warning text-white ms-2">
                            Loading...
                        </span>
                    </h5>
                    <a href="{% url 'website:course_requests_list' %}" class="btn btn-warning btn-sm">
                        {% translate "View All Requests" %}
                    </a>
                </div>
                <div id="course-requests-container" class="card-body">
                    <div class="d-flex justify-content-center py-3">
                        <div class="spinner-border text-warning" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Emergency Cancellation Requests Section -->
    {% if emergency_requests_count > 0 %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-body py-4 d-flex align-items-center">
                    <div class="text-warning me-3">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                    <div>
                        <h5 class="mb-0">{% translate "Emergency Cancellation Requests" %} <span class="badge bg-warning">{{ emergency_requests_count }}</span></h5>
                        <p class="text-muted mb-0">{% translate "There are emergency cancellation requests waiting for review." %}</p>
                    </div>
                    <div class="ms-auto">
                        <a href="{% url 'website:admin_cancellation_requests' %}" class="btn btn-warning">
                            {% translate "View All Requests" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- All Reservations Section -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 font-weight-bold">
                        {% if corporate %}
                            {{ corporate.legal_name }} - {% translate "Reservations" %}
                        {% else %}
                            {% translate "All Corporate Reservations" %}
                        {% endif %}
                    </h5>
                    {% if not corporate %}
                    <div>
                        <a href="{% url 'website:corporate_admins_view' %}" class="btn btn-light btn-sm">
                            <i class="fas fa-building me-1"></i> {% translate "Manage Corporates" %}
                        </a>
                    </div>
                    {% endif %}
                </div>
                <div class="card-body px-0 pt-0">
                    <div class="table-responsive p-0">
                        <table class="table table-hover align-items-center mb-0" id="reservations-table">
                            <thead class="bg-light">
                                <tr>
                                    <th class="text-uppercase text-dark font-weight-bolder px-3 py-3">{% translate "User" %}</th>
                                    <th class="text-uppercase text-dark font-weight-bolder px-3 py-3">{% translate "Course" %}</th>
                                    <th class="text-uppercase text-dark font-weight-bolder px-3 py-3">{% translate "Start Date" %}</th>
                                    <th class="text-uppercase text-dark font-weight-bolder px-3 py-3">{% translate "End Date" %}</th>
                                    <th class="text-uppercase text-dark font-weight-bolder px-3 py-3">{% translate "Sessions" %}</th>
                                    <th class="text-uppercase text-dark font-weight-bolder px-3 py-3">{% translate "Reserved On" %}</th>
                                    <th class="text-uppercase text-dark font-weight-bolder px-3 py-3">{% translate "Status" %}</th>
                                    <th class="text-uppercase text-dark font-weight-bolder px-3 py-3 text-center">{% translate "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for reservation in reservations %}
                                <tr class="border-bottom">
                                    <td class="px-3 py-3">
                                        <div class="d-flex align-items-center">
                                            <div class="icon-shape rounded-circle bg-primary text-white me-3 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 text-sm">{{ reservation.user.get_full_name }}</h6>
                                                <p class="text-xs text-secondary mb-0">{{ reservation.user.email }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-3 py-3">
                                        <div>
                                            <h6 class="mb-0 text-sm">{{ reservation.course_instance.course.name_en }}</h6>
                                            <p class="text-xs text-secondary mb-0">{{ reservation.course_instance.course.get_category_display }}</p>
                                        </div>
                                    </td>
                                    <td class="px-3 py-3">
                                        <p class="text-sm font-weight-bold mb-0">{{ reservation.course_instance.start_date|date:"M d, Y" }}</p>
                                        <p class="text-xs text-secondary mb-0">{{ reservation.course_instance.start_date|date:"H:i A" }}</p>
                                    </td>
                                    <td class="px-3 py-3">
                                        <p class="text-sm font-weight-bold mb-0">{{ reservation.course_instance.end_date|date:"M d, Y" }}</p>
                                        <p class="text-xs text-secondary mb-0">{{ reservation.course_instance.end_date|date:"H:i A" }}</p>
                                    </td>
                                    <td class="px-3 py-3">
                                        <span class="badge bg-info text-white px-3 py-2 rounded-pill">{{ reservation.course_instance.sessions.count }}</span>
                                    </td>
                                    <td class="px-3 py-3">
                                        <p class="text-sm mb-0">{{ reservation.created_at|date:"M d, Y" }}</p>
                                        <p class="text-xs text-secondary mb-0">{{ reservation.created_at|date:"H:i A" }}</p>
                                    </td>
                                    <td class="px-3 py-3">
                                        {% if reservation.status == 'UPCOMING' %}
                                            <span class="badge bg-success text-white px-3 py-2 rounded-pill">{% translate "Upcoming" %}</span>
                                        {% elif reservation.status == 'IN_PROGRESS' %}
                                            <span class="badge bg-primary text-white px-3 py-2 rounded-pill">{% translate "In Progress" %}</span>
                                        {% elif reservation.status == 'COMPLETED' %}
                                            <span class="badge bg-secondary text-white px-3 py-2 rounded-pill">{% translate "Completed" %}</span>
                                        {% elif reservation.status == 'CANCELLED' %}
                                            <span class="badge bg-danger text-white px-3 py-2 rounded-pill">{% translate "Cancelled" %}</span>
                                        {% elif reservation.status == 'WAITING_LIST' %}
                                            <span class="badge bg-warning text-white px-3 py-2 rounded-pill">{% translate "Waiting List" %}</span>
                                        {% else %}
                                            <span class="badge bg-info text-white px-3 py-2 rounded-pill">{{ reservation.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td class="px-3 py-3 text-center">
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'website:course_detail' reservation.course_instance.course.course_id %}" class="btn btn-outline-primary btn-sm me-2" data-bs-toggle="tooltip" data-bs-title="{% translate 'View Course' %}">
                                                <i class="fa fa-eye me-1"></i> {% translate "View" %}
                                            </a>
                                            <a href="{% url 'website:update_reservation' reservation.reservation_id %}" class="btn btn-outline-secondary btn-sm">
                                                <i class="fa fa-edit me-1"></i> {% translate "Edit" %}
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center py-5">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="fas fa-calendar-check text-secondary mb-3" style="font-size: 3rem;"></i>
                                            <p class="text-muted mb-0">{% translate "No reservations found" %}</p>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
        
        // Fetch corporate course requests
        fetchCorporateCourseRequests();
    });
    
    // Function to fetch and display corporate course requests
    function fetchCorporateCourseRequests() {
        fetch('{% url "website:course_requests_api" %}')
            .then(response => response.json())
            .then(data => {
                if (data.status !== 'success') {
                    throw new Error(data.message || 'Failed to fetch corporate course requests');
                }
                
                const pendingCount = data.pending_count;
                const pendingRequests = data.pending_requests;
                
                // Update count badge
                document.getElementById('pending-requests-count').textContent = pendingCount;
                
                // Get container
                const container = document.getElementById('course-requests-container');
                
                // Clear loading indicator
                container.innerHTML = '';
                
                if (pendingCount === 0) {
                    // Show no requests message
                    container.innerHTML = `
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle text-warning opacity-50 fa-3x mb-3"></i>
                            <h6 class="fw-bold">{% translate "No Pending Requests" %}</h6>
                            <p class="text-muted small">{% translate "There are no corporate course requests waiting for review." %}</p>
                        </div>
                    `;
                } else {
                    // Create a table to display the requests
                    const tableResponsive = document.createElement('div');
                    tableResponsive.className = 'table-responsive';
                    tableResponsive.innerHTML = `
                        <table class="table table-borderless align-middle">
                            <thead class="bg-warning bg-opacity-10">
                                <tr>
                                    <th class="text-uppercase text-warning fw-bold small px-3 py-2">{% translate "Corporate" %}</th>
                                    <th class="text-uppercase text-warning fw-bold small px-3 py-2">{% translate "Course" %}</th>
                                    <th class="text-uppercase text-warning fw-bold small px-3 py-2">{% translate "Type" %}</th>
                                    <th class="text-uppercase text-warning fw-bold small px-3 py-2">{% translate "Capacity" %}</th>
                                    <th class="text-uppercase text-warning fw-bold small px-3 py-2">{% translate "Date" %}</th>
                                    <th class="text-uppercase text-warning fw-bold small px-3 py-2 text-end">{% translate "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody id="requests-table-body">
                            </tbody>
                        </table>
                    `;
                    
                    container.appendChild(tableResponsive);
                    const tableBody = document.getElementById('requests-table-body');
                    
                    // Add up to 3 most recent requests
                    const requestsToShow = pendingRequests.slice(0, 3);
                    requestsToShow.forEach(request => {
                        // Format date
                        const createdAt = new Date(request.created_at);
                        
                        const row = document.createElement('tr');
                        row.className = 'border-bottom border-light';
                        row.innerHTML = `
                            <td class="px-3 py-3">
                                <div class="small fw-bold">${request.corporate.legal_name}</div>
                                <div class="small text-muted">${request.corporate_admin.email}</div>
                            </td>
                            <td class="px-3 py-3">
                                <div class="small fw-bold">${request.course ? request.course.name : '{% translate "Custom Request" %}'}</div>
                            </td>
                            <td class="px-3 py-3">
                                <div class="small">${request.course_type_display}</div>
                            </td>
                            <td class="px-3 py-3">
                                <span class="badge bg-warning text-white">${request.capacity} {% translate "seats" %}</span>
                            </td>
                            <td class="px-3 py-3">
                                <div class="small">${createdAt.toLocaleDateString()}</div>
                                <div class="small text-muted">${createdAt.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                            </td>
                            <td class="px-3 py-3 text-end">
                                <button class="btn btn-success btn-sm me-1" onclick="handleCourseRequest('${request.request_id}', 'APPROVED')">
                                    <i class="fas fa-check me-1"></i> {% translate "Approve" %}
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="handleCourseRequest('${request.request_id}', 'REJECTED')">
                                    <i class="fas fa-times me-1"></i> {% translate "Reject" %}
                                </button>
                            </td>
                        `;
                        
                        tableBody.appendChild(row);
                    });
                    
                    // If there are more requests than shown, add a note
                    if (pendingCount > 3) {
                        const moreMsg = document.createElement('div');
                        moreMsg.className = 'text-center py-2 small text-warning';
                        moreMsg.innerHTML = `
                            <i class="fas fa-info-circle me-1"></i> 
                            {% translate "and" %} <strong>${pendingCount - 3}</strong> {% translate "more pending requests" %}
                        `;
                        container.appendChild(moreMsg);
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching corporate course requests:', error);
                const container = document.getElementById('course-requests-container');
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                        <h6 class="fw-bold">{% translate "Error Loading Requests" %}</h6>
                        <p class="text-danger small">{% translate "Unable to load corporate course requests." %}</p>
                    </div>
                `;
                document.getElementById('pending-requests-count').textContent = "!";
            });
    }
    
    // Function to handle course request approval/rejection
    function handleCourseRequest(requestId, status) {
        // Show confirmation dialog
        if (!confirm(status === 'APPROVED' ? 
                    '{% translate "Are you sure you want to approve this course request?" %}' : 
                    '{% translate "Are you sure you want to reject this course request?" %}')) {
            return;
        }
        
        // Optionally prompt for admin notes
        let adminNotes = '';
        if (status === 'REJECTED') {
            adminNotes = prompt('{% translate "Please provide a reason for rejection (optional):" %}') || '';
        }
        
        // Send the update request
        fetch(`{% url "website:update_course_request_status" 0 %}`.replace('0', requestId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken(),
            },
            body: JSON.stringify({
                status: status,
                admin_notes: adminNotes
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Refresh the requests list
                fetchCorporateCourseRequests();
                
                // Show success message
                alert(status === 'APPROVED' ? 
                    '{% translate "Course request has been approved successfully." %}' : 
                    '{% translate "Course request has been rejected successfully." %}');
            } else {
                throw new Error(data.message || 'Failed to update course request');
            }
        })
        .catch(error => {
            console.error('Error handling course request:', error);
            alert('{% translate "Failed to update course request. Please try again." %}');
        });
    }
    
    // Helper function to get CSRF token
    function getCsrfToken() {
        const cookieValue = document.cookie
            .split('; ')
            .find(row => row.startsWith('csrftoken='))
            ?.split('=')[1];
        return cookieValue || '';
    }
</script>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        border-radius: 10px;
        overflow: hidden;
    }

    .table th {
        font-size: 0.8rem;
        font-weight: 600;
    }

    .table td {
        font-size: 0.9rem;
    }

    .icon-shape {
        transition: all 0.2s ease;
    }

    tr:hover .icon-shape {
        transform: scale(1.1);
    }

    .badge {
        font-weight: 500;
        letter-spacing: 0.5px;
    }
</style>
{% endblock %} 