{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "All Corporate Reservations" %} | GB Academy{% endblock %}

{% block extra_css %}
<style>
    /* Ensure modals stack properly above all content */
    #editReservationModal {
        z-index: 9999 !important; /* Force highest z-index */
    }
    
    /* Ensure modal content is above backdrop */
    #editReservationModal > div > div:last-child {
        position: relative;
        z-index: 10000 !important; /* Even higher z-index */
    }
    
    /* Make sure notification panels have lower z-index */
    .mb-8.bg-yellow-900\/20 { /* Escaped / for CSS selector */
        z-index: 10;
        position: relative;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-white mb-8 flex items-center">
        <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
        </svg>
        {{ corporate.legal_name }} - {% trans "User Reservations" %}
    </h1>

    {% if messages %}
    <div class="mb-8">
        {% for message in messages %}
        <div class="p-4 mb-4 text-sm rounded-lg {% if message.tags == 'success' %}bg-green-800/30 backdrop-blur-md text-green-400 border border-green-400/20{% elif message.tags == 'error' %}bg-red-800/30 backdrop-blur-md text-red-400 border border-red-400/20{% elif message.tags == 'warning' %}bg-yellow-800/30 backdrop-blur-md text-yellow-400 border border-yellow-400/20{% else %}bg-blue-800/30 backdrop-blur-md text-blue-400 border border-blue-400/20{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Corporate Info Card -->
    <div class="mb-8 bg-blue-900/20 backdrop-blur-md rounded-lg border border-blue-800/30 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-blue-800/30">
            <h2 class="text-xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                </svg>
                {% trans "Corporate Information" %}
            </h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <p class="text-gray-400 text-sm">{% trans "Corporate Admin" %}</p>
                    <p class="text-white">{{ corporate.corporate_admin.get_full_name }}</p>
                    <p class="text-blue-400 text-sm">{{ corporate.corporate_admin.email }}</p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm">{% trans "Contact" %}</p>
                    <p class="text-white">{{ corporate.phone_number }}</p>
                    <p class="text-blue-400 text-sm">{{ corporate.get_category_display }}</p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm">{% trans "Capacity" %}</p>
                    <p class="text-white">{{ corporate.capacity }} {% trans "users" %}</p>
                    <p class="text-blue-400 text-sm">{% trans "Corporate ID" %}: {{ corporate.corporate_id }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Form - Match page background -->
    <div class="rounded-lg border border-blue-800 overflow-hidden shadow-lg mb-8">
        <div class="p-4">            
            <form method="GET" action="{% url 'website:my_corporate_reservations' %}">
                <div class="flex items-center">
                    <div class="flex-1 grid grid-cols-6 gap-x-2">
                        <div>
                            <label for="course_id" class="block text-sm font-medium text-white mb-1">{% trans "Course" %}</label>
                            <select id="course_id" name="course_id" class="w-full px-2 py-2 bg-blue-800/50 border border-blue-700 rounded-md text-white text-sm focus:ring-primary focus:border-primary">
                                <option value="">{% trans "All Courses" %}</option>
                                {% for course in courses %}
                                <option value="{{ course.course_id }}" {% if filters.course_id == course.course_id|slugify %}selected{% endif %}>{{ course.name_en }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div>
                            <label for="instance_id" class="block text-sm font-medium text-white mb-1">{% trans "Course Instance" %}</label>
                            <select id="instance_id" name="instance_id" class="w-full px-2 py-2 bg-blue-800/50 border border-blue-700 rounded-md text-white text-sm focus:ring-primary focus:border-primary">
                                <option value="">{% trans "All Instances" %}</option>
                                {% for instance in course_instances %}
                                <option value="{{ instance.instance_id }}" {% if filters.instance_id == instance.instance_id|slugify %}selected{% endif %}>{{ instance.course.name_en }} ({{ instance.start_date|date:"M d, Y" }})</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div>
                            <label for="user_id" class="block text-sm font-medium text-white mb-1">{% trans "User" %}</label>
                            <select id="user_id" name="user_id" class="w-full px-2 py-2 bg-blue-800/50 border border-blue-700 rounded-md text-white text-sm focus:ring-primary focus:border-primary">
                                <option value="">{% trans "All Users" %}</option>
                                {% for user in corporate_users %}
                                <option value="{{ user.id }}" {% if filters.user_id == user.id|slugify %}selected{% endif %}>{{ user.get_full_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div>
                            <label for="status" class="block text-sm font-medium text-white mb-1">{% trans "Status" %}</label>
                            <select id="status" name="status" class="w-full px-2 py-2 bg-blue-800/50 border border-blue-700 rounded-md text-white text-sm focus:ring-primary focus:border-primary">
                                <option value="">{% trans "All Statuses" %}</option>
                                <option value="UPCOMING" {% if filters.status == 'UPCOMING' %}selected{% endif %}>{% trans "Upcoming" %}</option>
                                <option value="IN_PROGRESS" {% if filters.status == 'IN_PROGRESS' %}selected{% endif %}>{% trans "In Progress" %}</option>
                                <option value="COMPLETED" {% if filters.status == 'COMPLETED' %}selected{% endif %}>{% trans "Completed" %}</option>
                                <option value="WAITING_LIST" {% if filters.status == 'WAITING_LIST' %}selected{% endif %}>{% trans "Waiting List" %}</option>
                                <option value="CANCELLED" {% if filters.status == 'CANCELLED' %}selected{% endif %}>{% trans "Cancelled" %}</option>
                                <option value="WAITING_TO_PAY" {% if filters.status == 'WAITING_TO_PAY' %}selected{% endif %}>{% trans "Waiting to Pay" %}</option>
                            </select>
                        </div>
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-white mb-1">{% trans "Start Date (From)" %}</label>
                            <input type="date" id="start_date" name="start_date" value="{{ filters.start_date|default:'' }}" placeholder="mm/dd/yyyy"
                                   class="w-full px-2 py-2 bg-blue-800/50 border border-blue-700 rounded-md text-white text-sm focus:ring-primary focus:border-primary">
                        </div>
                        <div>
                            <label for="end_date" class="block text-sm font-medium text-white mb-1">{% trans "End Date (To)" %}</label>
                            <input type="date" id="end_date" name="end_date" value="{{ filters.end_date|default:'' }}" placeholder="mm/dd/yyyy"
                                   class="w-full px-2 py-2 bg-blue-800/50 border border-blue-700 rounded-md text-white text-sm focus:ring-primary focus:border-primary">
                        </div>
                    </div>
                    <div class="flex space-x-2 items-end pl-3">
                        <a href="{% url 'website:my_corporate_reservations' %}" class="px-4 py-2 text-sm font-medium text-white hover:text-gray-200 border border-blue-700 rounded-md">
                            {% trans "Reset" %}
                        </a>
                        <button type="submit" class="px-4 py-2 text-sm font-medium rounded-md bg-primary text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            {% trans "Apply Filters" %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Corporate Cancellation Requests Notification Panel -->
    <div class="mb-8 bg-gray-800/50 backdrop-blur-md rounded-lg border border-gray-700/50 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-gray-700/50 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                </svg>
                {% trans "Corporate Cancellation Requests" %}
                <span id="corporate-pending-count" class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full bg-yellow-500 text-yellow-900">
                    Loading...
                </span>
            </h2>
            <!-- Button to open the modal -->
            <div class="flex items-center">
                <button id="view-all-requests-btn" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-yellow-600 text-white hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                    {% trans "View All Requests" %}
                </button>
            </div>
        </div>

        <!-- Container to display a preview of requests -->
        <div id="corporate-requests-preview-container" class="p-4"> 
            <!-- Loading spinner -->
            <div id="corporate-requests-loading" class="py-8 flex justify-center">
                <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
            
            <!-- No requests message -->
            <div id="no-corporate-requests-preview" class="hidden">
                <div class="flex flex-col items-center justify-center py-8">
                    <div class="rounded-full bg-blue-800/50 p-3 mb-4">
                        <svg class="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-white">{% trans "No Pending Requests" %}</h3>
                    <p class="mt-2 text-sm text-gray-400">{% trans "There are no corporate cancellation requests waiting for review." %}</p>
                </div>
            </div>
            
            <!-- Preview of pending requests (up to 3) -->
            <div id="corporate-requests-preview-list" class="space-y-4 hidden">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-white/10 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white">{% trans "All Corporate User Reservations" %}</h2>
            <div class="flex space-x-2">
                {% comment %} <button id="showCancellationRequestsBtn" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-yellow-600 text-white hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                    </svg>
                    <span id="cancellationRequestsCount">{% trans "Cancellation Requests" %}</span>
                </button> {% endcomment %}
                <a href="{% url 'website:my_corporate_users' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                    </svg>
                    {% trans "Manage Users" %}
                </a>
                <a href="{% url 'website:corporate_admin_calendar' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-primary text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    {% trans "Calendar View" %}
                </a>
            </div>
        </div>

        {% if reservations %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-white/10">
                <thead class="bg-white/5">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "User" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Course" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Start Date" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "End Date" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Sessions" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Reserved On" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Status" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-transparent divide-y divide-white/10">
                    {% for reservation in reservations %}
                    <tr class="hover:bg-white/5">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-white">{{ reservation.user.get_full_name }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.user.email }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-white">{{ reservation.course_instance.course.name_en }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.course_instance.course.get_category_display }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ reservation.course_instance.start_date|date:"M d, Y" }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.course_instance.start_date|date:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ reservation.course_instance.end_date|date:"M d, Y" }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.course_instance.end_date|date:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ reservation.course_instance.sessions.count }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ reservation.created_at|date:"M d, Y" }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.created_at|date:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if reservation.status == 'UPCOMING' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-800/30 text-green-400">
                                {{ reservation.get_status_display }}
                            </span>
                            {% elif reservation.status == 'COMPLETED' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-800/30 text-gray-400">
                                {{ reservation.get_status_display }}
                            </span>
                            {% elif reservation.status == 'IN_PROGRESS' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-800/30 text-blue-400">
                                {{ reservation.get_status_display }}
                            </span>
                            {% elif reservation.status == 'WAITING_LIST' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-800/30 text-yellow-400">
                                {{ reservation.get_status_display }}
                            </span>
                            {% elif reservation.status == 'CANCELLED' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-800/30 text-red-400">
                                {{ reservation.get_status_display }}
                            </span>
                            {% elif reservation.status == 'WAITING_TO_PAY' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-800/30 text-yellow-400">
                                {% trans "Waiting to Pay" %}
                            </span>
                            {% else %}
                                {# Fallback to date-based status if status field is not available #}
                                {% if reservation.course_instance.start_date > now %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-800/30 text-green-400">
                                    {% trans "Upcoming" %}
                                </span>
                                {% elif reservation.course_instance.end_date < now %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-800/30 text-gray-400">
                                    {% trans "Completed" %}
                                </span>
                                {% else %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-800/30 text-blue-400">
                                    {% trans "In Progress" %}
                                </span>
                                {% endif %}
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex justify-end items-center space-x-4">
                                <a href="{% url 'website:course_detail' course_id=reservation.course_instance.course.course_id %}" class="text-blue-400 hover:text-blue-300 text-sm">
                                    {% trans "View Course" %}
                                </a>
                                <!-- We're using a button to open modal here; corporate admins can't directly edit -->
                                <button type="button" 
                                        onclick="viewReservationDetails('{{ reservation.reservation_id }}')"
                                        class="text-blue-400 hover:text-blue-300 text-sm">
                                    {% trans "Details" %}
                                </button>
                                <button type="button"
                                        onclick="viewAttendance('{{ reservation.course_instance.instance_id }}', '{{ reservation.user.id }}', '{{ reservation.course_instance.course.name_en|escapejs }}', '{{ reservation.user.get_full_name|escapejs }}')"
                                        class="text-blue-400 hover:text-blue-300 text-sm">
                                    {% trans "Attendance" %}
                                </button>
                                <button type="button"
                                        onclick="confirmDeleteReservation('{{ reservation.reservation_id }}', '{{ reservation.user.get_full_name|escapejs }}', '{{ reservation.course_instance.course.name_en|escapejs }}')"
                                        class="text-red-400 hover:text-red-300 text-sm">
                                    {% trans "Cancel" %}
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="px-6 py-12 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Reservations" %}</h3>
            <p class="mt-1 text-sm text-gray-400">{% trans "There are no course reservations for your corporate users yet." %}</p>
            <div class="mt-6">
                <a href="{% url 'website:my_corporate_users' %}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    {% trans "Add Corporate Users" %}
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- View Reservation Details Modal -->
<div id="viewReservationModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-16 px-4 pb-20 text-center">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-black opacity-75"></div>
        </div>

        <!-- Modal panel -->
        <div class="inline-block align-middle bg-blue-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full relative mx-auto">
            <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white" id="modalTitle">
                            {% trans "Reservation Details" %}
                        </h3>
                        <div class="mt-6 space-y-4" id="reservation-details-container">
                            <!-- Reservation details will be populated here -->
                            <div class="flex justify-center">
                                <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-blue-800/50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" onclick="closeReservationModal()" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Close" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Cancellation Requests Modal -->
<div id="cancellationRequestsModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-black opacity-75"></div>
        </div>

        <!-- Modal panel -->
        <div class="inline-block align-middle bg-blue-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-4xl sm:w-full relative mx-auto">
            <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white">
                            {% trans "Corporate Cancellation Requests" %}
                            <button type="button" onclick="closeCancellationRequestsModal()" class="float-right text-gray-400 hover:text-white">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </h3>
                        
                        <div class="mt-2 mb-4">
                            <select id="cancelRequestsFilterSelect" class="px-3 py-2 bg-blue-800/50 border border-blue-700 rounded-md text-white text-sm focus:ring-primary focus:border-primary">
                                <option value="PENDING">{% trans "Pending Requests" %}</option>
                                <option value="APPROVED">{% trans "Approved Requests" %}</option>
                                <option value="REJECTED">{% trans "Rejected Requests" %}</option>
                                <option value="ALL">{% trans "All Requests" %}</option>
                            </select>
                        </div>
                        
                        <div id="cancellationRequestsLoading" class="py-8 flex justify-center">
                            <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </div>
                        
                        <div id="noCancellationRequests" class="py-8 text-center hidden">
                            <div class="flex flex-col items-center justify-center">
                                <div class="rounded-full bg-blue-800/50 p-3 mb-4">
                                    <svg class="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Pending Requests" %}</h3>
                                <p class="mt-1 text-sm text-gray-400">{% trans "There are no pending emergency cancellation requests to review." %}</p>
                            </div>
                        </div>
                        
                        <div id="cancellationRequestsList" class="mt-4 hidden">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-white/10">
                                    <thead class="bg-white/5">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                                {% trans "User" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                                {% trans "Course" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                                {% trans "Reason" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                                {% trans "Requested On" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                                                {% trans "Actions" %}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody id="cancellationRequestsTable" class="bg-transparent divide-y divide-white/10">
                                        <!-- Requests will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- Recent Processed Requests Section -->
                        <div id="recentProcessedRequests" class="mt-8 pt-6 border-t border-white/10 hidden">
                            <h3 class="text-lg leading-6 font-medium text-white mb-4">
                                {% trans "Recent Processed Requests" %}
                            </h3>
                            
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-white/10">
                                    <thead class="bg-white/5">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                                {% trans "User" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                                {% trans "Course" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                                {% trans "Status" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                                {% trans "Processed On" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                                {% trans "Notes" %}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody id="processedRequestsTable" class="bg-transparent divide-y divide-white/10">
                                        <!-- Processed requests will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-blue-800/50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="closeCancellationModalBtn" onclick="closeCancellationRequestsModal()" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Close" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add CSRF token for AJAX requests -->
{% csrf_token %}

<!-- Attendance Modal -->
<div id="attendanceModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
            <div class="bg-gray-900 px-6 pt-6 pb-5 sm:p-8 sm:pb-6">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-white" id="attendanceModalTitle">
                                {% trans "Course Attendance" %}
                            </h3>
                            <button type="button" id="closeAttendanceModal" class="text-gray-400 hover:text-white">
                                <svg class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        
                        <div id="attendanceModalContent">
                            <div class="flex items-center justify-center h-32">
                                <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-white"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Global function to close the cancellation requests modal
    function closeCancellationRequestsModal() {
        const modal = document.getElementById('cancellationRequestsModal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }
    
    document.addEventListener('DOMContentLoaded', function() {
        // Course Instance Filtering
        const courseSelect = document.getElementById('course_id');
        const instanceSelect = document.getElementById('instance_id');
        
        // Store all course instances for filtering
        const allInstances = [];
        {% for instance in course_instances %}
        allInstances.push({
            id: "{{ instance.instance_id }}",
            courseId: "{{ instance.course.course_id }}",
            name: "{{ instance.course.name_en }} ({{ instance.start_date|date:'M d, Y' }})"
        });
        {% endfor %}
        
        // Filter course instances when course selection changes
        courseSelect.addEventListener('change', function() {
            const selectedCourseId = this.value;
            
            // Clear the instance select
            instanceSelect.innerHTML = '<option value="">{% trans "All Instances" %}</option>';
            
            // If no course is selected, we're done
            if (!selectedCourseId) {
                // Add all instances back
                allInstances.forEach(function(instance) {
                    const option = document.createElement('option');
                    option.value = instance.id;
                    option.textContent = instance.name;
                    instanceSelect.appendChild(option);
                });
                return;
            }
            
            // Filter instances for the selected course
            const filteredInstances = allInstances.filter(function(instance) {
                return instance.courseId === selectedCourseId;
            });
            
            // Add filtered instances to the select
            filteredInstances.forEach(function(instance) {
                const option = document.createElement('option');
                option.value = instance.id;
                option.textContent = instance.name;
                instanceSelect.appendChild(option);
            });
        });
        
        // Elements cache
        const pendingBadge = document.getElementById('corporate-pending-count');
        
        // Check for pending cancellation requests on page load
        checkPendingCancellationRequests();
        
        // Add event listeners
        const showCancellationRequestsBtn = document.getElementById('showCancellationRequestsBtn');
        if (showCancellationRequestsBtn) {
            showCancellationRequestsBtn.addEventListener('click', openCancellationRequestsModal);
        }
        
        const cancelRequestsFilterSelect = document.getElementById('cancelRequestsFilterSelect');
        if (cancelRequestsFilterSelect) {
            cancelRequestsFilterSelect.addEventListener('change', loadCancellationRequests);
        }
        
        // Function to check for pending cancellation requests
        function checkPendingCancellationRequests() {
            // Show loading state
            const loadingElement = document.getElementById('corporate-requests-loading');
            const noRequestsElement = document.getElementById('no-corporate-requests-preview');
            const requestListElement = document.getElementById('corporate-requests-preview-list');
            
            if (loadingElement) loadingElement.classList.remove('hidden');
            if (noRequestsElement) noRequestsElement.classList.add('hidden');
            if (requestListElement) requestListElement.classList.add('hidden');
            
            fetch('/api/corporate-cancel-requests/count/')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        const count = data.count;
                        // Update the existing button text/badge (kept for backward compatibility or other UI elements)
                        const countBadge = document.getElementById('cancellationRequestsCount');
                        if (countBadge) {
                            countBadge.textContent = `{% trans "Cancellation Requests" %} (${count})`;
                            countBadge.classList.toggle('text-yellow-300', count > 0); // Add emphasis if count > 0
                        }
                        
                        // Update the new notification panel badge
                        const panelCountBadge = document.getElementById('corporate-pending-count');
                        if (panelCountBadge) {
                            panelCountBadge.textContent = count;
                            if (count === 0) {
                                panelCountBadge.classList.add('hidden'); // Hide badge if count is 0
                                
                                // Show "No pending requests" message
                                if (loadingElement) loadingElement.classList.add('hidden');
                                if (noRequestsElement) noRequestsElement.classList.remove('hidden');
                            } else {
                                panelCountBadge.classList.remove('hidden');
                                
                                // Load preview of pending requests
                                loadCancellationRequestsPreview();
                            }
                        }
                        
                    } else {
                        console.error('Error fetching cancellation request count:', data.message);
                        // Optionally hide or show an error state for the badge
                        const panelCountBadge = document.getElementById('corporate-pending-count');
                        if (panelCountBadge) panelCountBadge.textContent = 'Error';
                        
                        // Hide loading
                        if (loadingElement) loadingElement.classList.add('hidden');
                    }
                })
                .catch(error => {
                    console.error('Error fetching cancellation request count:', error);
                    const panelCountBadge = document.getElementById('corporate-pending-count');
                    if (panelCountBadge) panelCountBadge.textContent = 'Error';
                    
                    // Hide loading
                    if (loadingElement) loadingElement.classList.add('hidden');
                });
        }
        
        // Function to load preview of pending cancellation requests (up to 3)
        function loadCancellationRequestsPreview() {
            const loadingElement = document.getElementById('corporate-requests-loading');
            const noRequestsElement = document.getElementById('no-corporate-requests-preview');
            const requestListElement = document.getElementById('corporate-requests-preview-list');
            
            // Ensure preview container is visible
            const previewContainer = document.getElementById('corporate-requests-preview-container');
            if (previewContainer) previewContainer.classList.remove('hidden');
            
            // Fetch pending cancellation requests
            fetch('/api/corporate-cancel-requests/?status=PENDING')
                .then(response => response.json())
            .then(data => {
                    // Hide loading
                    if (loadingElement) loadingElement.classList.add('hidden');
                    
                    if (data.status === 'success') {
                        const requests = data.requests;
                        
                        if (requests.length === 0) {
                            // Show empty state
                            if (noRequestsElement) noRequestsElement.classList.remove('hidden');
                        } else {
                            // Show preview list and populate with up to 3 requests
                            if (requestListElement) {
                                requestListElement.innerHTML = ''; // Clear previous content
                                requestListElement.classList.remove('hidden');
                                
                                // Add table header row (styled like the tbody rows for consistency in preview)
                                const headerRow = document.createElement('div');
                                headerRow.className = 'grid grid-cols-5 gap-4 px-4 py-2 text-xs font-medium text-gray-400 uppercase tracking-wider bg-gray-900/30 rounded-t-md';
                                headerRow.innerHTML = `
                                    <div class="col-span-1">{% trans "User" %}</div>
                                    <div class="col-span-1">{% trans "Course" %}</div>
                                    <div class="col-span-1">{% trans "Start Date" %}</div>
                                    <div class="col-span-1">{% trans "Reason" %}</div>
                                    <div class="col-span-1 text-right">{% trans "Actions" %}</div>
                                `;
                                requestListElement.appendChild(headerRow);
                                
                                // Display up to 3 requests
                                const previewRequests = requests.slice(0, 3);
                                
                                previewRequests.forEach((request, index) => {
                                    const requestRow = document.createElement('div');
                                    // Add appropriate rounding based on index
                                    const rowClasses = index === previewRequests.length - 1 && requests.length <= 3 ? 'rounded-b-md' : '';
                                    requestRow.className = `grid grid-cols-5 gap-4 px-4 py-3 bg-gray-800/50 border-t border-gray-700/50 items-center ${rowClasses}`;
                                    
                                    requestRow.innerHTML = `
                                        <div class="col-span-1 text-sm text-white">
                                            <div>${request.user.name}</div>
                                            <div class="text-xs text-gray-400">${request.user.email}</div>
                                        </div>
                                        <div class="col-span-1 text-sm text-white">
                                            <div>${request.course.name}</div>
                                            <div class="text-xs text-gray-400">{% trans "Professional Development" %}</div> {# Assuming category, adjust if needed #}
                                        </div>
                                        <div class="col-span-1 text-sm text-white">
                                            <div>${new Date(request.course.start_date).toLocaleDateString('en-US', {month:'numeric', day:'numeric', year:'numeric'})}</div>
                                            <div class="text-xs text-gray-400">${new Date(request.course.start_date).toLocaleTimeString('en-US', {hour:'2-digit', minute:'2-digit', hour12:true})}</div>
                                        </div>
                                        <div class="col-span-1 text-sm text-white truncate" title="${request.reason}">
                                            ${request.reason.length > 50 ? request.reason.substring(0, 50) + '...' : request.reason}
                                        </div>
                                        <div class="col-span-1 text-right">
                                            <button onclick="openCancellationRequestsModal()" class="text-yellow-400 hover:text-yellow-300 text-sm font-medium">
                                                {% trans "Review" %}
                                            </button>
                                        </div>
                                    `;
                                    
                                    requestListElement.appendChild(requestRow);
                                });
                                
                                // Add "View all" button if there are more than 3 requests
                                if (requests.length > 3) {
                                    const viewMoreDiv = document.createElement('div');
                                    viewMoreDiv.className = 'text-center py-3 bg-gray-800/50 border-t border-gray-700/50 rounded-b-md';
                                    viewMoreDiv.innerHTML = `
                                        <button id="view-more-requests" onclick="openCancellationRequestsModal()" class="text-blue-400 hover:text-blue-300 text-sm font-medium">
                                            {% trans "View all" %} ${requests.length} {% trans "pending requests" %}
                                        </button>
                                    `;
                                    requestListElement.appendChild(viewMoreDiv);
                                }
                            }
                        }
                    } else {
                        console.error('Error loading cancellation requests preview:', data.message);
                        // Show error state
                        if (noRequestsElement) {
                            noRequestsElement.classList.remove('hidden');
                            noRequestsElement.innerHTML = `
                                <div class="text-center py-6">
                                    <p class="text-red-400">{% trans "Error loading requests" %}</p>
                    </div>
                            `;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    if (loadingElement) loadingElement.classList.add('hidden');
                    
                    // Show error state
                    if (noRequestsElement) {
                        noRequestsElement.classList.remove('hidden');
                        noRequestsElement.innerHTML = `
                            <div class="text-center py-6">
                                <p class="text-red-400">{% trans "Error loading requests" %}</p>
                        </div>
                        `;
                    }
                });
        }
        
        // Function to open cancellation requests modal
        function openCancellationRequestsModal() {
            // Reset filter to "Pending" and load requests
            document.getElementById('cancelRequestsFilterSelect').value = 'PENDING';
            
        // Show the modal
            document.getElementById('cancellationRequestsModal').classList.remove('hidden');
            
            // Load cancellation requests
            loadCancellationRequests();
        }
        
        // Function to load cancellation requests
        function loadCancellationRequests() {
            const statusFilterElem = document.getElementById('cancelRequestsFilterSelect');
            const statusFilter = statusFilterElem ? statusFilterElem.value : 'PENDING';
            const requestsContainer = document.getElementById('cancellationRequestsList');
            const processedContainer = document.getElementById('recentProcessedRequests');
            const loadingElement = document.getElementById('cancellationRequestsLoading');
            const emptyElement = document.getElementById('noCancellationRequests');
            
            // Show loading, hide content
            if (loadingElement) loadingElement.classList.remove('hidden');
            if (requestsContainer) requestsContainer.classList.add('hidden');
            if (emptyElement) emptyElement.classList.add('hidden');
            if (processedContainer) processedContainer.classList.add('hidden');
            
            // Fetch cancellation requests
            fetch(`/api/corporate-cancel-requests/?status=${statusFilter}`)
                .then(response => response.json())
                .then(data => {
                    // Hide loading
                    if (loadingElement) loadingElement.classList.add('hidden');
                    
                    if (data.status === 'success') {
                        const requests = data.requests;
                        
                        if (requests.length === 0) {
                            // Show empty state
                            if (emptyElement) emptyElement.classList.remove('hidden');
                        } else {
                            // Separate pending and processed requests
                            const pendingRequests = requests.filter(req => req.status === 'PENDING');
                            const processedRequests = requests.filter(req => req.status !== 'PENDING');
                            
                            // Show main requests list (either pending or all, depending on filter)
                            if (requestsContainer) {
                                requestsContainer.classList.remove('hidden');
                                populateCancellationRequests(statusFilter === 'PENDING' ? pendingRequests : requests);
                            }
                            
                            // Show processed requests separately if we're viewing pending requests or if explicitly viewing processed
                            if (processedContainer && (statusFilter === 'PENDING' || statusFilter === 'APPROVED' || statusFilter === 'REJECTED')) {
                                const showProcessed = statusFilter === 'PENDING' ? processedRequests.length > 0 : false;
                                
                                if (showProcessed) {
                                    processedContainer.classList.remove('hidden');
                                    populateProcessedRequests(processedRequests);
                                } else if (statusFilter !== 'PENDING') {
                                    // If explicitly filtering for processed requests, we'll only show the main table
                                    processedContainer.classList.add('hidden');
                                }
                            }
                        }
                    } else {
                        console.error('Error loading cancellation requests:', data.message);
                        alert(data.message || "{% trans 'Error loading cancellation requests' %}");
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    if (loadingElement) loadingElement.classList.add('hidden');
                    alert("{% trans 'Error communicating with the server' %}");
                });
        }
        
        // Function to populate cancellation requests
        function populateCancellationRequests(requests) {
            const container = document.getElementById('cancellationRequestsTable');
            if (!container) {
                console.error('Cancellation requests table element not found');
                return;
            }
            
            container.innerHTML = '';
            
            requests.forEach(request => {
                // Create table row
                const row = document.createElement('tr');
                row.className = 'hover:bg-white/5';
                
                // Determine status badge color
                let statusBadgeClass = 'bg-yellow-800/30 text-yellow-400';
                if (request.status === 'APPROVED') {
                    statusBadgeClass = 'bg-green-800/30 text-green-400';
                } else if (request.status === 'REJECTED') {
                    statusBadgeClass = 'bg-red-800/30 text-red-400';
                }
                
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-white">${request.user.name}</div>
                        <div class="text-xs text-gray-400">${request.user.email}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-white">${request.course.name}</div>
                        <div class="text-xs text-gray-400">${request.course.start_date} - ${request.course.end_date}</div>
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm text-white">${request.reason}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-white">${request.created_at}</div>
                        <div class="text-xs text-gray-400">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusBadgeClass}">
                                ${request.status_display}
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right">
                        ${request.status === 'PENDING' ? `
                            <div class="flex justify-end space-x-2">
                                <button type="button" onclick="approveRequest('${request.request_id}')" 
                                    class="inline-flex items-center px-3 py-1 border border-transparent text-xs leading-4 font-medium rounded-md shadow-sm bg-green-600 text-white hover:bg-green-700 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-green-500">
                                    {% trans "Approve" %}
                                </button>
                                <button type="button" onclick="rejectRequest('${request.request_id}')" 
                                    class="inline-flex items-center px-3 py-1 border border-transparent text-xs leading-4 font-medium rounded-md shadow-sm bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-red-500">
                                    {% trans "Reject" %}
                                </button>
                            </div>
                            <div class="mt-2">
                                <textarea id="adminNotes-${request.request_id}" 
                                    placeholder="{% trans 'Admin notes (optional)' %}" 
                                    class="w-full p-2 bg-blue-700 text-white rounded border border-blue-600 text-xs"></textarea>
                            </div>
                        ` : `
                            <div class="text-xs text-gray-400">
                                ${request.admin_notes ? request.admin_notes : '{% trans "No admin notes" %}'}
                    </div>
                        `}
                    </td>
                `;
                
                container.appendChild(row);
            });
        }
        
        // Function to approve request
        window.approveRequest = function(requestId) {
            processRequest(requestId, 'APPROVED');
        };
        
        // Function to reject request
        window.rejectRequest = function(requestId) {
            processRequest(requestId, 'REJECTED');
        };
        
        // Function to process request (approve or reject)
        function processRequest(requestId, status) {
            const adminNotes = document.getElementById(`adminNotes-${requestId}`).value;
            
            // Confirm before processing
            const confirmMessage = status === 'APPROVED' 
                ? "{% trans 'Are you sure you want to approve this cancellation request? This will cancel the reservation.' %}"
                : "{% trans 'Are you sure you want to reject this cancellation request? The reservation will remain active.' %}";
                
            if (!confirm(confirmMessage)) {
                return;
            }
            
            // Prepare form data
            const formData = new FormData();
            formData.append('request_id', requestId);
            formData.append('status', status);
            formData.append('admin_notes', adminNotes);
            
            // Process the request
            fetch('{% url "website:process_cancellation_request_api" %}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Show success message
                    alert(data.message || "{% trans 'Request processed successfully' %}");
                    
                    // Reload cancellation requests to show updated status
                    loadCancellationRequests();
                    
                    // Update the pending badge count
                    checkPendingCancellationRequests();
                    
                    // If we approved a cancellation, also refresh the main reservations table
                    if (status === 'APPROVED' && typeof refreshReservations === 'function') {
                        refreshReservations();
                    }
                } else {
                    // Show error message
                    alert(data.message || "{% trans 'Error processing request' %}");
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert("{% trans 'Error communicating with the server' %}");
            });
        }
        
        // Helper function to get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        // Function to populate processed cancellation requests table
        function populateProcessedRequests(requests) {
            const container = document.getElementById('processedRequestsTable');
            if (!container) {
                console.error('Processed requests table element not found');
                return;
            }
            
            container.innerHTML = '';
            
            requests.forEach(request => {
                // Create table row
                const row = document.createElement('tr');
                row.className = 'hover:bg-white/5';
                
                // Determine status badge color
                let statusBadgeClass = 'bg-yellow-800/30 text-yellow-400';
                if (request.status === 'APPROVED') {
                    statusBadgeClass = 'bg-green-800/30 text-green-400';
                } else if (request.status === 'REJECTED') {
                    statusBadgeClass = 'bg-red-800/30 text-red-400';
                }
                
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-white">${request.user.name}</div>
                        <div class="text-xs text-gray-400">${request.user.email}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-white">${request.course.name}</div>
                        <div class="text-xs text-gray-400">${request.course.start_date} - ${request.course.end_date}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-medium rounded-full ${statusBadgeClass}">
                            ${request.status_display}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-white">${request.updated_at}</div>
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm text-white">${request.admin_notes || '-'}</div>
                    </td>
                `;
                
                container.appendChild(row);
            });
        }
        
        // Make functions available to the window
        window.openCancellationRequestsModal = openCancellationRequestsModal;
        window.closeCancellationRequestsModal = closeCancellationRequestsModal;

        // Add event listener for the new "View All Requests" button in the notification panel
        const viewAllRequestsBtn = document.getElementById('view-all-requests-btn');
        if (viewAllRequestsBtn) {
            viewAllRequestsBtn.addEventListener('click', openCancellationRequestsModal);
        }
    });

    // Function to view reservation details
    function viewReservationDetails(reservationId) {
        // Show the modal
        const modal = document.getElementById('viewReservationModal');
        if (modal) {
            modal.classList.remove('hidden');
        }
        
        // Show loading state
        const detailsContainer = document.getElementById('reservation-details-container');
        if (detailsContainer) {
            detailsContainer.innerHTML = `
                <div class="flex justify-center">
                    <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </div>
            `;
        }
        
        // Fetch reservation details
        fetch(`/api/reservations/${reservationId}/details/`)
            .then(response => response.json())
            .then(data => {
                if (detailsContainer) {
                    // Format sessions list if available
                    let sessionsHtml = '';
                    if (data.sessions && data.sessions.length > 0) {
                        sessionsHtml = `
                            <div class="mt-4">
                                <h4 class="text-sm font-medium text-white mb-2">{% trans "Sessions" %}</h4>
                                <div class="bg-blue-800/30 rounded overflow-hidden">
                                    <div class="grid grid-cols-3 gap-2 p-2 text-xs font-medium text-white/70 bg-blue-900/50">
                                        <div>{% trans "Date" %}</div>
                                        <div>{% trans "Start Time" %}</div>
                                        <div>{% trans "End Time" %}</div>
                                    </div>
                                    ${data.sessions.map(session => {
                                        const start = new Date(session.start_date);
                                        const end = new Date(session.end_date);
                                        return `
                                            <div class="grid grid-cols-3 gap-2 p-2 text-sm border-t border-blue-800/50">
                                                <div class="text-white">${start.toLocaleDateString()}</div>
                                                <div class="text-white">${start.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                                                <div class="text-white">${end.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                                            </div>
                                        `;
                                    }).join('')}
                                </div>
                            </div>
                        `;
                    }
                    
                    // Format dates
                    const startDate = new Date(data.course_instance.start_date);
                    const endDate = new Date(data.course_instance.end_date);
                    const createdAt = new Date(data.created_at);
                    
                    // Populate the details container
                    detailsContainer.innerHTML = `
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-white/70">{% trans "Course" %}</h4>
                                <p class="text-lg font-medium text-white">${data.course.name}</p>
                                <p class="text-sm text-blue-400">${data.course.category}</p>
                            </div>
                            
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <h4 class="text-sm font-medium text-white/70">{% trans "Start Date" %}</h4>
                                    <p class="text-base text-white">${startDate.toLocaleDateString()}</p>
                                    <p class="text-sm text-blue-400">${startDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</p>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-white/70">{% trans "End Date" %}</h4>
                                    <p class="text-base text-white">${endDate.toLocaleDateString()}</p>
                                    <p class="text-sm text-blue-400">${endDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</p>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-medium text-white/70">{% trans "User" %}</h4>
                                <p class="text-base text-white">${data.user.name}</p>
                                <p class="text-sm text-blue-400">${data.user.email}</p>
                            </div>
                            
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <h4 class="text-sm font-medium text-white/70">{% trans "Status" %}</h4>
                                    <p class="text-base text-white">${data.status_display}</p>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-white/70">{% trans "Reserved On" %}</h4>
                                    <p class="text-base text-white">${createdAt.toLocaleDateString()}</p>
                                    <p class="text-sm text-blue-400">${createdAt.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</p>
                                </div>
                            </div>
                            
                            ${sessionsHtml}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error fetching reservation details:', error);
                if (detailsContainer) {
                    detailsContainer.innerHTML = `
                        <div class="text-center text-red-400">
                            <p>{% trans "Error loading reservation details. Please try again." %}</p>
                        </div>
                    `;
                }
            });
    }
    
    // Function to confirm and cancel a reservation
    function confirmDeleteReservation(reservationId, userName, courseName) {
        if (confirm(`{% trans "Are you sure you want to cancel the reservation for" %} ${userName} (${courseName})?`)) {
            // Get CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
            
            // Use fetch with POST method
            fetch(`/api/reservations/${reservationId}/cancel/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken,
                    'Content-Type': 'application/json'
                },
                // No body needed as the request ID is in the URL
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Show success message and reload the page
                    alert(data.message || "{% trans 'Reservation cancelled successfully' %}");
                    window.location.reload();
                } else {
                    // Show error message
                    alert(data.message || "{% trans 'Error cancelling reservation' %}");
                }
            })
            .catch(error => {
                console.error('Error cancelling reservation:', error);
                alert("{% trans 'Network error. Please try again.' %}");
            });
        }
    }
    
    // Function to close the reservation details modal
    function closeReservationModal() {
        const modal = document.getElementById('viewReservationModal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    // Function to view attendance for a user's course instance
    function viewAttendance(courseInstanceId, userId, courseName, userName) {
        // Get modal elements
        const modal = document.getElementById('attendanceModal');
        const modalTitle = document.getElementById('attendanceModalTitle');
        const modalContent = document.getElementById('attendanceModalContent');
        
        if (!modal || !modalTitle || !modalContent) {
            console.error('Attendance modal elements not found');
            return;
        }
        
        // Update modal title with course and user name
        if (userName) {
            modalTitle.innerHTML = `<span class="text-blue-400">${courseName}</span> - ${userName} - {% trans "Attendance" %}`;
        } else {
            modalTitle.innerHTML = `<span class="text-blue-400">${courseName}</span> - {% trans "Attendance" %}`;
        }
        
        // Show loading spinner
        modalContent.innerHTML = `
            <div class="flex items-center justify-center h-32">
                <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-white"></div>
            </div>
        `;
        
        // Show the modal
        modal.classList.remove('hidden');
        
        // Build URL with parameters
        let url = `{% url 'website:admin_attendance_api' %}?course_instance_id=${courseInstanceId}`;
        if (userId) {
            url += `&user_id=${userId}`;
        }
        
        // Fetch attendance data
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.status === 'success') {
                    if (data.sessions.length === 0) {
                        // No sessions found
                        modalContent.innerHTML = `
                            <div class="text-center py-10">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-300">{% trans "No Sessions Found" %}</h3>
                                <p class="mt-1 text-sm text-gray-400">{% trans "There are no sessions associated with this course instance." %}</p>
                            </div>
                        `;
                        return;
                    }
                    
                    // Create table with sessions - different format depending on if viewing one user or all users
                    if (userId) {
                        // Single user view
                        let tableHtml = `
                            <div class="overflow-x-auto rounded-lg border border-gray-700" style="max-height: 65vh;">
                                <table class="min-w-full divide-y divide-gray-700" style="table-layout: fixed;">
                                    <thead class="bg-gray-800">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 15%;">
                                                {% trans "Session ID" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 15%;">
                                                {% trans "Date" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 20%;">
                                                {% trans "Time" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 15%;">
                                                {% trans "Room" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 15%;">
                                                {% trans "Status" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 20%;">
                                                {% trans "Attendance" %}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-gray-900 divide-y divide-gray-800">
                        `;
                        
                        data.sessions.forEach(session => {
                            let statusClass, statusLabel;
                            
                            // Determine session status styling
                            switch(session.status) {
                                case 'upcoming':
                                    statusClass = 'bg-blue-900/30 text-blue-400 border border-blue-800/50';
                                    statusLabel = '{% trans "Upcoming" %}';
                                    break;
                                case 'in_progress':
                                    statusClass = 'bg-green-900/30 text-green-400 border border-green-800/50';
                                    statusLabel = '{% trans "In Progress" %}';
                                    break;
                                case 'completed':
                                    statusClass = 'bg-gray-800/30 text-gray-400 border border-gray-700/50';
                                    statusLabel = '{% trans "Completed" %}';
                                    break;
                                default:
                                    statusClass = 'bg-gray-800 text-white';
                                    statusLabel = session.status;
                            }
                            
                            // Determine attendance status
                            let attendanceStatus;
                            const attendance = session.attendance || {};
                            const firstHalfAttended = attendance.first_half || false;
                            const secondHalfAttended = attendance.second_half || false;
                            const fullyAttended = firstHalfAttended && secondHalfAttended;
                            const partiallyAttended = firstHalfAttended || secondHalfAttended;
                            
                            if (fullyAttended) {
                                attendanceStatus = `
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/30 text-green-400 border border-green-800/50">
                                        <svg class="mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        {% trans "Attended" %}
                                    </span>
                                `;
                            } else {
                                if (session.status === 'completed') {
                                    if (partiallyAttended) {
                                        attendanceStatus = `
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-900/30 text-yellow-400 border border-yellow-800/50">
                                                <svg class="mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01" />
                                                </svg>
                                                {% trans "Partial" %} (${firstHalfAttended ? '1st' : '2nd'})
                                            </span>
                                        `;
                                    } else {
                                        attendanceStatus = `
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-900/30 text-red-400 border border-red-800/50">
                                                <svg class="mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                                {% trans "Absent" %}
                                            </span>
                                        `;
                                    }
                                } else {
                                    attendanceStatus = `
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-800/50 text-gray-400 border border-gray-700/50">
                                            {% trans "Not Started" %}
                                        </span>
                                    `;
                                }
                            }
                            
                            tableHtml += `
                                <tr class="hover:bg-gray-800/50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                        ${session.session_id}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        ${session.date}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        ${session.time}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        ${session.room}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-medium rounded-full ${statusClass}">
                                            ${statusLabel}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        ${attendanceStatus}
                                    </td>
                                </tr>
                            `;
                        });
                        
                        tableHtml += `
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-5 bg-blue-900/20 border border-blue-800/50 rounded-md p-4 text-sm text-blue-300">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="font-medium text-blue-400 mb-1">{% trans "Attendance Information" %}</p>
                                        <p>{% trans "Attendance is marked by trainers during each session. Corporate admins can view attendance records but cannot modify them." %}</p>
                                    </div>
                                </div>
                            </div>
                        `;
                        
                        modalContent.innerHTML = tableHtml;
                        
                    } else {
                        // Multi-user view - more complex table showing all users and their attendance
                        // First create a map of users for quick reference
                        const usersMap = {};
                        if (data.users) {
                            data.users.forEach(user => {
                                usersMap[user.id] = user.name;
                            });
                        }
                        
                        let tableHtml = `
                            <div class="overflow-x-auto rounded-lg border border-gray-700" style="max-height: 65vh;">
                                <table class="min-w-full divide-y divide-gray-700">
                                    <thead class="bg-gray-800">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                                {% trans "Session" %}
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                                {% trans "Date" %}
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                                {% trans "Status" %}
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                                {% trans "Attendance Summary" %}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-gray-900 divide-y divide-gray-800">
                        `;
                        
                        data.sessions.forEach(session => {
                            let statusClass, statusLabel;
                            
                            // Determine session status styling
                            switch(session.status) {
                                case 'upcoming':
                                    statusClass = 'bg-blue-900/30 text-blue-400 border border-blue-800/50';
                                    statusLabel = '{% trans "Upcoming" %}';
                                    break;
                                case 'in_progress':
                                    statusClass = 'bg-green-900/30 text-green-400 border border-green-800/50';
                                    statusLabel = '{% trans "In Progress" %}';
                                    break;
                                case 'completed':
                                    statusClass = 'bg-gray-800/30 text-gray-400 border border-gray-700/50';
                                    statusLabel = '{% trans "Completed" %}';
                                    break;
                                default:
                                    statusClass = 'bg-gray-800 text-white';
                                    statusLabel = session.status;
                            }
                            
                            // Calculate attendance summary
                            const attendances = session.attendances || {};
                            let attendedCount = 0;
                            // Count only fully attended (both halves) users
                            Object.values(attendances).forEach(attendance => {
                                if (attendance.first_half && attendance.second_half) {
                                    attendedCount++;
                                }
                            });
                            const totalUsers = data.users ? data.users.length : 0;
                            const attendancePercent = totalUsers > 0 ? Math.round((attendedCount / totalUsers) * 100) : 0;
                            
                            // Create attendance summary badge
                            let summaryClass, summaryLabel;
                            if (session.status === 'upcoming') {
                                summaryClass = 'bg-gray-800/50 text-gray-400';
                                summaryLabel = '{% trans "Not Started" %}';
                            } else {
                                if (attendancePercent >= 80) {
                                    summaryClass = 'bg-green-900/30 text-green-400 border border-green-800/50';
                                } else if (attendancePercent >= 50) {
                                    summaryClass = 'bg-yellow-900/30 text-yellow-400 border border-yellow-800/50';
                                } else {
                                    summaryClass = 'bg-red-900/30 text-red-400 border border-red-800/50';
                                }
                                summaryLabel = `${attendedCount} / ${totalUsers} (${attendancePercent}%)`;
                            }
                            
                            tableHtml += `
                                <tr class="hover:bg-gray-800/50">
                                    <td class="px-6 py-4 text-sm text-white">
                                        ${session.session_id}<br>
                                        <span class="text-xs text-gray-400">${session.room}</span>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-white">
                                        ${session.date}<br>
                                        <span class="text-xs text-gray-400">${session.time}</span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-medium rounded-full ${statusClass}">
                                            ${statusLabel}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-medium rounded-full ${summaryClass}">
                                            ${summaryLabel}
                                        </span>
                                        <div class="mt-2">
                                            <button onclick="toggleAttendanceDetails('${session.session_id}')" class="text-xs text-blue-400 hover:text-blue-300">
                                                {% trans "Show Details" %}
                                            </button>
                                        </div>
                                        <div id="attendance-details-${session.session_id}" class="hidden mt-2 bg-gray-800/50 rounded p-2 text-xs">
                                            <div class="max-h-40 overflow-y-auto">
                        `;
                        
                            // Add individual attendance records for this session
                            let attendanceDetails = '';
                            if (session.status !== 'upcoming') {
                                // Sort by name for better readability
                                const userIds = Object.keys(attendances).sort((a, b) => {
                                    const nameA = attendances[a].user_name || usersMap[a] || a;
                                    const nameB = attendances[b].user_name || usersMap[b] || b;
                                    return nameA.localeCompare(nameB);
                                });
                                
                                userIds.forEach(userId => {
                                    const attendance = attendances[userId];
                                    const userName = attendance.user_name || usersMap[userId] || userId;
                                    const firstHalfAttended = attendance.first_half || false;
                                    const secondHalfAttended = attendance.second_half || false;
                                    const fullyAttended = firstHalfAttended && secondHalfAttended;
                                    const partiallyAttended = firstHalfAttended || secondHalfAttended;
                                    
                                    let attendanceIcon, attendanceText;
                                    if (fullyAttended) {
                                        attendanceIcon = `<svg class="inline-block h-3 w-3 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>`;
                                        attendanceText = '{% trans "Present" %}';
                                    } else if (partiallyAttended) {
                                        attendanceIcon = `<svg class="inline-block h-3 w-3 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01" />
                                        </svg>`;
                                        attendanceText = `{% trans "Partial" %} (${firstHalfAttended ? '1st' : '2nd'})`;
                                    } else {
                                        attendanceIcon = `<svg class="inline-block h-3 w-3 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>`;
                                        attendanceText = '{% trans "Absent" %}';
                                    }
                                    
                                    attendanceDetails += `
                                        <div class="mb-1 flex justify-between">
                                            <span>${userName}</span>
                                            <span>${attendanceIcon} ${attendanceText}</span>
                                        </div>
                                    `;
                                });
                                
                                // Add users who don't have attendance records (presumed absent)
                                if (data.users) {
                                    data.users.forEach(user => {
                                        if (!attendances[user.id]) {
                                            attendanceDetails += `
                                                <div class="mb-1 flex justify-between text-gray-400">
                                                    <span>${user.name}</span>
                                                    <span>
                                                        <svg class="inline-block h-3 w-3 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                        </svg>
                                                        {% trans "Absent" %}
                                                    </span>
                                                </div>
                                            `;
                                        }
                                    });
                                }
                            } else {
                                attendanceDetails = `<p class="text-center text-gray-400">{% trans "This session has not started yet" %}</p>`;
                            }
                            
                            tableHtml += attendanceDetails;
                            
                            tableHtml += `
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        `;
                        });
                        
                        tableHtml += `
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-5 bg-blue-900/20 border border-blue-800/50 rounded-md p-4 text-sm text-blue-300">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="font-medium text-blue-400 mb-1">{% trans "Attendance Information" %}</p>
                                        <p>{% trans "Attendance is marked by trainers during each session. Click 'Show Details' to see individual attendance records." %}</p>
                                    </div>
                                </div>
                            </div>
                        `;
                        
                        modalContent.innerHTML = tableHtml;
                    }
                } else {
                    // Handle error state
                    modalContent.innerHTML = `
                        <div class="text-center py-10">
                            <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-red-300">{% trans "Error" %}</h3>
                            <p class="mt-1 text-sm text-gray-400">${data.message || '{% trans "An error occurred while fetching attendance data." %}'}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error fetching attendance data:', error);
                modalContent.innerHTML = `
                    <div class="text-center py-10">
                        <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-red-300">{% trans "Error" %}</h3>
                        <p class="mt-1 text-sm text-gray-400">{% trans "An error occurred while fetching attendance data." %}</p>
                    </div>
                `;
            });
    }
    
    // Function to toggle attendance details sections
    function toggleAttendanceDetails(sessionId) {
        const detailsElement = document.getElementById(`attendance-details-${sessionId}`);
        if (detailsElement) {
            // Toggle visibility
            if (detailsElement.classList.contains('hidden')) {
                detailsElement.classList.remove('hidden');
            } else {
                detailsElement.classList.add('hidden');
            }
        }
    }

    // Add event listener to close the attendance modal
    document.addEventListener('DOMContentLoaded', function() {
        const closeAttendanceModalBtn = document.getElementById('closeAttendanceModal');
        if (closeAttendanceModalBtn) {
            closeAttendanceModalBtn.addEventListener('click', function() {
                const modal = document.getElementById('attendanceModal');
                if (modal) {
                    modal.classList.add('hidden');
                }
            });
        }
    });
</script>
{% endblock %} 