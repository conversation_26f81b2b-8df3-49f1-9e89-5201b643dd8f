{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "My Reservations" %} | GB Academy{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-wrap justify-between items-center mb-4">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
            </svg>
            {% trans "My Reservations" %}
        </h1>
        <a href="{% url 'website:courses' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-primary text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            {% trans "Enroll in New Course" %}
        </a>
    </div>

    {% if messages %}
    <div class="mb-8">
        {% for message in messages %}
        <div class="p-4 mb-4 text-sm rounded-lg {% if message.tags == 'success' %}bg-green-800/30 backdrop-blur-md text-green-400 border border-green-400/20{% elif message.tags == 'error' %}bg-red-800/30 backdrop-blur-md text-red-400 border border-red-400/20{% elif message.tags == 'warning' %}bg-yellow-800/30 backdrop-blur-md text-yellow-400 border border-yellow-400/20{% else %}bg-blue-800/30 backdrop-blur-md text-blue-400 border border-blue-400/20{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Pending Surveys Section -->
    <div class="mb-8 bg-blue-900/20 backdrop-blur-md rounded-lg border border-blue-800/30 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-blue-800/30 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                {% trans "Course Feedback" %}
                <span id="pending-surveys-count" class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full bg-blue-500 text-white">
                    Loading...
                </span>
            </h2>
            <a href="{% url 'website:individual_user_surveys' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                {% trans "View All Feedback" %}
            </a>
        </div>

        <div id="pending-surveys-container" class="p-4">
            <div class="flex justify-center">
                <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Pending Questionnaires Section -->
    <div class="mb-8 bg-green-900/20 backdrop-blur-md rounded-lg border border-green-800/30 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-green-800/30 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                </svg>
                {% trans "Course Questionnaires" %}
                <span id="pending-questionnaires-count" class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full bg-green-500 text-white">
                    Loading...
                </span>
            </h2>
            <a href="{% url 'website:individual_user_questionnaires' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-green-600 text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                {% trans "View All Questionnaires" %}
            </a>
        </div>

        <div id="pending-questionnaires-container" class="p-4">
            <div class="flex justify-center">
                <svg class="animate-spin h-8 w-8 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
        </div>
    </div>

    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-white/10 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white">{% trans "My Course Reservations" %}</h2>
            <div class="flex gap-2">
                {% comment %} <a href="{% url 'website:individual_user_surveys' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6.3 6.3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {% trans "View Course Feedback" %}
                </a> {% endcomment %}
                <a href="{% url 'website:courses' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-primary text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    {% trans "Enroll in New Course" %}
                </a>
            </div>
        </div>

        {% if user_reservations %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-white/10">
                <thead class="bg-white/5">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Course" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Start Date" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "End Date" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Sessions" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Reserved On" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Status" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-transparent divide-y divide-white/10">
                    {% for reservation in user_reservations %}
                    <tr class="hover:bg-white/5">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-white">{{ reservation.course_instance.course.name_en }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.course_instance.course.get_category_display }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ reservation.course_instance.start_date|date:"M d, Y" }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.course_instance.start_date|date:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ reservation.course_instance.end_date|date:"M d, Y" }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.course_instance.end_date|date:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ reservation.course_instance.sessions.count }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ reservation.created_at|date:"M d, Y" }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.created_at|date:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if reservation.status == 'UPCOMING' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-800/30 text-green-400">
                                {{ reservation.get_status_display }}
                            </span>
                            {% elif reservation.status == 'COMPLETED' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-800/30 text-gray-400">
                                {{ reservation.get_status_display }}
                            </span>
                            {% elif reservation.status == 'IN_PROGRESS' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-800/30 text-blue-400">
                                {{ reservation.get_status_display }}
                            </span>
                            {% elif reservation.status == 'WAITING_TO_PAY' %}
                            <div class="badge badge-warning badge-sm">{% trans "Waiting to Pay" %}</div>
                            {% elif reservation.status == 'WAITING_LIST' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-800/30 text-yellow-400">
                                {{ reservation.get_status_display }}
                            </span>
                            {% elif reservation.status == 'CANCELLED' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-800/30 text-red-400">
                                {{ reservation.get_status_display }}
                            </span>
                            {% else %}
                                {# Fallback to date-based status if status field is not available #}
                                {% if reservation.course_instance.start_date > now %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-800/30 text-green-400">
                                    {% trans "Upcoming" %}
                                </span>
                                {% elif reservation.course_instance.end_date < now %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-800/30 text-gray-400">
                                    {% trans "Completed" %}
                                </span>
                                {% else %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-800/30 text-blue-400">
                                    {% trans "In Progress" %}
                                </span>
                                {% endif %}
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <a href="{% url 'website:course_detail' course_id=reservation.course_instance.course.course_id %}" class="text-primary hover:text-primary/80 mr-4">
                                {% trans "View" %}
                            </a>
                            <button type="button" 
                                    class="text-blue-500 hover:text-blue-400 mr-4 view-attendance-btn" 
                                    data-course-instance-id="{{ reservation.course_instance.instance_id }}"
                                    data-course-name="{{ reservation.course_instance.course.name_en }}">
                                {% trans "Attendance" %}
                            </button>
                            {% if reservation.status == 'UPCOMING' or reservation.status == 'WAITING_TO_PAY' or reservation.status == 'WAITING_LIST' %}
                            <form method="POST" action="{% url 'website:user_reservations' %}" class="inline" onsubmit="return confirm('{% trans "Are you sure you want to cancel this reservation? This action cannot be undone." %}');">
                                {% csrf_token %}
                                <input type="hidden" name="reservation_id" value="{{ reservation.reservation_id }}">
                                <button type="submit" name="cancel_reservation" class="text-red-500 hover:text-red-400">
                                    {% trans "Cancel" %}
                                </button>
                            </form>
                            <div class="text-xs text-gray-400 mt-1 cancellation-deadline" data-reservation-id="{{ reservation.reservation_id }}">
                                {% trans "Cancellation deadline" %}: <span class="deadline-display">{% trans "Loading..." %}</span>
                            </div>
                            {% elif not reservation.status and reservation.course_instance.start_date > now %}
                            <form method="POST" action="{% url 'website:user_reservations' %}" class="inline" onsubmit="return confirm('{% trans "Are you sure you want to cancel this reservation? This action cannot be undone." %}');">
                                {% csrf_token %}
                                <input type="hidden" name="reservation_id" value="{{ reservation.reservation_id }}">
                                <button type="submit" name="cancel_reservation" class="text-red-500 hover:text-red-400">
                                    {% trans "Cancel" %}
                                </button>
                            </form>
                            <div class="text-xs text-gray-400 mt-1 cancellation-deadline" data-reservation-id="{{ reservation.reservation_id }}">
                                {% trans "Cancellation deadline" %}: <span class="deadline-display">{% trans "Loading..." %}</span>
                            </div>
                            {% endif %}
                            {% if reservation.status == 'WAITING_TO_PAY' %}
                            <form method="POST" action="{% url 'website:check_before_payment' reservation.reservation_id %}" class="inline ml-2">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-primary btn-sm">
                                    {% trans "Pay Now" %}
                                </button>
                            </form>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="px-6 py-12 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Reservations" %}</h3>
            <p class="mt-1 text-sm text-gray-400">{% trans "You haven't enrolled in any courses yet." %}</p>
            <div class="mt-6">
                <a href="{% url 'website:courses' %}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    {% trans "Browse Available Courses" %}
                </a>
            </div>
        </div>
        {% endif %}
    </div>
    
    {% if user_reservations %}
    <div class="mt-6 bg-white/5 backdrop-blur-md rounded-lg border border-white/10 p-4 text-sm text-gray-300">
        <h3 class="font-medium mb-2">{% trans "Cancellation Policy" %}</h3>
        <p>{% trans "Please note that you can only cancel your reservation before the cancellation deadline, which is calculated based on business days before the course start time. Business days exclude weekends (Friday and Saturday) and holidays." %}</p>
        <p class="mt-2">{% trans "For example, if the cancellation period is 3 business days, you must cancel at least 3 business days before the course starts. If the course starts on Monday, and the cancellation period is 3 business days, you must cancel by the previous Tuesday (assuming no holidays in between)." %}</p>
        <p class="mt-2">{% trans "If you need to cancel after the deadline has passed, please submit an emergency cancellation request." %}</p>
    </div>
    {% endif %}
</div>

<!-- Emergency Cancellation Modal -->
<div id="emergencyCancellationModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-gray-900 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white" id="emergencyModalTitle">
                            {% trans "Emergency Cancellation Request" %}
                        </h3>
                        <div class="mt-4 mb-4 p-3 bg-blue-900/30 border border-blue-700/50 rounded-md">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-blue-300">
                                        {% trans "This form is for emergency cancellation requests after the standard cancellation deadline has passed. Please provide a valid reason and supporting documentation if available. Your request will be reviewed by administration." %}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <form id="emergencyCancellationForm" method="POST" action="{% url 'website:emergency_cancellation_request' %}" enctype="multipart/form-data">
                            {% csrf_token %}
                            <input type="hidden" id="emergency_reservation_id" name="reservation_id" value="">
                            
                            <div class="mb-4">
                                <label for="reason" class="block text-sm font-medium text-gray-300">
                                    {% trans "Reason for Cancellation" %} <span class="text-red-500">*</span>
                                </label>
                                <textarea id="reason" name="reason" rows="4" required 
                                    class="mt-1 block w-full rounded-md bg-gray-800 border-gray-600 text-white shadow-sm focus:border-primary focus:ring-primary"></textarea>
                                <p class="mt-1 text-sm text-gray-400">
                                    {% trans "Please explain in detail why you need to cancel after the standard deadline." %}
                                </p>
                            </div>
                            
                            <div class="mb-4">
                                <label for="attachment" class="block text-sm font-medium text-gray-300">
                                    {% trans "Supporting Document" %} <span class="text-gray-400">({% trans "optional" %})</span>
                                </label>
                                <input type="file" id="attachment" name="attachment" 
                                    class="mt-1 block w-full rounded-md bg-gray-800 border-gray-600 text-white shadow-sm focus:border-primary focus:ring-primary">
                                <p class="mt-1 text-sm text-gray-400">
                                    {% trans "Upload any supporting document (e.g., medical certificate)." %}
                                </p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="submitEmergencyCancellationBtn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Submit Request" %}
                </button>
                <button type="button" id="closeEmergencyCancellationBtn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Cancel" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Cancellation Confirmation Modal -->
<div id="cancellationConfirmModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-gray-900 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white">
                            {% trans "Confirm Cancellation" %}
                        </h3>
                        <div class="mt-4 mb-4 p-3 bg-gray-800 border border-gray-700 rounded-md text-gray-300">
                            <p>{% trans "Please note that you can only cancel your reservation before the cancellation deadline, which is calculated based on business days before the course start time. Business days exclude weekends (Friday and Saturday) and holidays." %}</p>
                            <p class="mt-2">{% trans "For example, if the cancellation period is 3 business days, you must cancel at least 3 business days before the course starts. If the course starts on Monday, and the cancellation period is 3 business days, you must cancel by the previous Tuesday (assuming no holidays in between)." %}</p>
                            <p class="mt-2">{% trans "If you need to cancel after the deadline has passed, please submit an emergency cancellation request." %}</p>
                        </div>
                        <form id="cancellationForm" method="POST" action="{% url 'website:user_reservations' %}">
                            {% csrf_token %}
                            <input type="hidden" id="cancellation_reservation_id" name="reservation_id" value="">
                            <input type="hidden" name="cancel_reservation" value="true">
                        </form>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirmCancellationBtn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Confirm Cancel" %}
                </button>
                <button type="button" id="closeCancellationBtn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Cancel" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Modal -->
<div id="attendanceModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
            <div class="bg-gray-900 px-6 pt-6 pb-5 sm:p-8 sm:pb-6">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-white" id="attendanceModalTitle">
                                {% trans "Course Attendance" %}
                            </h3>
                            <button type="button" id="closeAttendanceModal" class="text-gray-400 hover:text-white">
                                <svg class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        
                        <div id="attendanceModalContent">
                            <div class="flex items-center justify-center h-32">
                                <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-white"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Function to fetch and display pending surveys
function fetchIndividualPendingSurveys() {
    const pendingSurveysContainer = document.getElementById('pending-surveys-container');
    const pendingSurveysCount = document.getElementById('pending-surveys-count');
    
    fetch('/api/surveys/individual/pending-count/')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Update count badge
                pendingSurveysCount.textContent = data.count;
                
                // If there are pending surveys, display them
                if (data.count > 0) {
                    let surveysHtml = '<div class="grid grid-cols-1 gap-4">';
                    
                    // Take only up to 3 surveys to show
                    const surveysToShow = data.surveys.slice(0, 3);
                    
                    surveysToShow.forEach(survey => {
                        surveysHtml += `
                            <div class="bg-blue-800/30 rounded-lg p-4 border border-blue-700/30 flex justify-between items-center">
                                <div>
                                    <div class="font-medium text-white">${survey.survey.title}</div>
                                    <div class="text-sm text-gray-300">${survey.course.name}</div>
                                    <div class="text-xs text-blue-300 mt-1">${survey.survey.question_count} ${survey.survey.question_count === 1 ? '{% trans "question" %}' : '{% trans "questions" %}'}</div>
                                </div>
                                <a href="/my-surveys/take/${survey.survey.survey_id}/${survey.reservation.reservation_id}/" 
                                   class="bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded-md shadow-sm">
                                    {% trans "Complete Survey" %}
                                </a>
                            </div>
                        `;
                    });
                    
                    // If there are more than 3 surveys
                    if (data.surveys.length > 3) {
                        surveysHtml += `
                            <div class="text-center mt-2">
                                <a href="{% url 'website:individual_user_surveys' %}" class="text-blue-400 hover:text-blue-300 text-sm">
                                    {% trans "View all" %} ${data.count} {% trans "pending surveys" %}
                                </a>
                            </div>
                        `;
                    }
                    
                    surveysHtml += '</div>';
                    pendingSurveysContainer.innerHTML = surveysHtml;
                } else {
                    // No pending surveys
                    pendingSurveysContainer.innerHTML = `
                        <div class="text-center py-6">
                            <svg class="mx-auto h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Pending Feedback" %}</h3>
                            <p class="mt-1 text-sm text-gray-400">{% trans "You don't have any pending course feedback to complete." %}</p>
                        </div>
                    `;
                }
            } else {
                // Error fetching surveys
                pendingSurveysContainer.innerHTML = `
                    <div class="text-center py-6">
                        <svg class="mx-auto h-10 w-10 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-white">{% trans "Error" %}</h3>
                        <p class="mt-1 text-sm text-red-400">{% trans "An error occurred while fetching your pending surveys." %}</p>
                    </div>
                `;
                pendingSurveysCount.textContent = '0';
            }
        })
        .catch(error => {
            console.error('Error fetching pending surveys:', error);
            pendingSurveysContainer.innerHTML = `
                <div class="text-center py-6">
                    <svg class="mx-auto h-10 w-10 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-white">{% trans "Error" %}</h3>
                    <p class="mt-1 text-sm text-red-400">{% trans "An error occurred while fetching your pending surveys." %}</p>
                </div>
            `;
            pendingSurveysCount.textContent = '0';
        });
}

    // Function to fetch and display pending questionnaires
    function fetchIndividualPendingQuestionnaires() {
        const pendingQuestionnairesContainer = document.getElementById('pending-questionnaires-container');
        const pendingQuestionnairesCount = document.getElementById('pending-questionnaires-count');
        
        fetch('/api/questionnaires/individual/pending-count/?detailed=true')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Update count badge
                    pendingQuestionnairesCount.textContent = data.count;
                    
                    // If there are pending questionnaires, display them
                    if (data.count > 0) {
                        let questionnairesHtml = '<div class="grid grid-cols-1 gap-4">';
                        
                        // Take only up to 3 questionnaires to show
                        const questionnairesToShow = data.questionnaires.slice(0, 3);
                        
                        questionnairesToShow.forEach(questionnaire => {
                            questionnairesHtml += `
                                <div class="bg-green-800/30 rounded-lg p-4 border border-green-700/30 flex justify-between items-center">
                                    <div>
                                        <div class="font-medium text-white">${questionnaire.questionnaire.title}</div>
                                        <div class="text-sm text-gray-300">${questionnaire.course.name}</div>
                                        <div class="text-xs text-green-300 mt-1">${questionnaire.questionnaire.question_count} ${questionnaire.questionnaire.question_count === 1 ? '{% trans "question" %}' : '{% trans "questions" %}'}</div>
                                    </div>
                                    <a href="/my-individual-questionnaires/take/${questionnaire.questionnaire.questionnaire_id}/${questionnaire.reservation.reservation_id || ''}" 
                                       class="bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-4 rounded-md shadow-sm">
                                        {% trans "Complete Questionnaire" %}
                                    </a>
                                </div>
                            `;
                        });
                        
                        // If there are more than 3 questionnaires
                        if (data.questionnaires.length > 3) {
                            questionnairesHtml += `
                                <div class="text-center mt-2">
                                    <a href="{% url 'website:individual_user_questionnaires' %}" class="text-green-400 hover:text-green-300 text-sm">
                                        {% trans "View all" %} ${data.count} {% trans "pending questionnaires" %}
                                    </a>
                                </div>
                            `;
                        }
                        
                        questionnairesHtml += '</div>';
                        pendingQuestionnairesContainer.innerHTML = questionnairesHtml;
                    } else {
                        // No pending questionnaires
                        pendingQuestionnairesContainer.innerHTML = `
                            <div class="text-center py-6">
                                <svg class="mx-auto h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Pending Questionnaires" %}</h3>
                                <p class="mt-1 text-sm text-gray-400">{% trans "You don't have any pending course questionnaires to complete." %}</p>
                            </div>
                        `;
                    }
                } else {
                    // Error fetching questionnaires
                    pendingQuestionnairesContainer.innerHTML = `
                        <div class="text-center py-6">
                            <svg class="mx-auto h-10 w-10 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-white">{% trans "Error" %}</h3>
                            <p class="mt-1 text-sm text-red-400">{% trans "An error occurred while fetching your pending questionnaires." %}</p>
                        </div>
                    `;
                    pendingQuestionnairesCount.textContent = '0';
                }
            })
            .catch(error => {
                console.error('Error fetching pending questionnaires:', error);
                pendingQuestionnairesContainer.innerHTML = `
                    <div class="text-center py-6">
                        <svg class="mx-auto h-10 w-10 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-white">{% trans "Error" %}</h3>
                        <p class="mt-1 text-sm text-red-400">{% trans "An error occurred while fetching your pending questionnaires." %}</p>
                    </div>
                `;
                pendingQuestionnairesCount.textContent = '0';
            });
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Find all cancellation deadline elements
        const deadlineElements = document.querySelectorAll('.cancellation-deadline');
        
        // Function to format the deadline display
        function formatDeadline(hours, minutes, businessDays) {
            if (hours === 0 && minutes === 0) {
                return '{% trans "Deadline has passed" %}';
            }
            
            let result = '';
            if (hours > 0) {
                result += hours + ' {% trans "hours" %}';
            }
            
            if (minutes > 0) {
                if (result) result += ', ';
                result += minutes + ' {% trans "minutes" %}';
            }
            
            return result + ' (' + businessDays + ' {% trans "business days before start" %})';
        }
        
        // Function to fetch and update a single deadline
        function updateDeadline(element) {
            const reservationId = element.getAttribute('data-reservation-id');
            const displayElement = element.querySelector('.deadline-display');
            
            // Make API request
            fetch(`{% url 'website:get_cancellation_deadline' %}?reservation_id=${reservationId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        displayElement.textContent = formatDeadline(
                            data.hours_remaining, 
                            data.minutes_remaining,
                            data.cancellation_deadline_business_days
                        );
                        
                        // If cancellation is no longer allowed, we might want to disable the cancel button
                        // and show emergency cancellation option instead
                        if (!data.can_cancel) {
                            const form = element.previousElementSibling;
                            if (form && form.tagName === 'FORM') {
                                const button = form.querySelector('button[name="cancel_reservation"]');
                                if (button) {
                                    button.disabled = true;
                                    button.classList.add('opacity-50', 'cursor-not-allowed');
                                    button.title = '{% trans "Cancellation deadline has passed" %}';
                                    
                                    // Add emergency cancellation button if it doesn't exist yet
                                    if (!form.nextElementSibling || !form.nextElementSibling.classList.contains('emergency-cancel-btn')) {
                                        const emergencyBtn = document.createElement('button');
                                        emergencyBtn.className = 'ml-2 text-yellow-500 hover:text-yellow-400 emergency-cancel-btn';
                                        emergencyBtn.textContent = '{% trans "Emergency Cancel" %}';
                                        emergencyBtn.setAttribute('data-reservation-id', reservationId);
                                        
                                        // Check if this reservation already has a pending emergency cancellation request
                                        if (data.has_emergency_request) {
                                            emergencyBtn.disabled = true;
                                            emergencyBtn.classList.add('opacity-50', 'cursor-not-allowed');
                                            emergencyBtn.title = '{% trans "You have already submitted an emergency cancellation request for this reservation" %}';
                                            
                                            // Add explanatory text
                                            const infoText = document.createElement('div');
                                            infoText.className = 'text-xs text-yellow-500/80 mt-1';
                                            infoText.textContent = '{% trans "You can only submit one emergency cancellation request per reservation" %}';
                                            form.parentNode.insertBefore(infoText, form.nextSibling.nextSibling);
                                        } else {
                                            emergencyBtn.addEventListener('click', function(e) {
                                                e.preventDefault();
                                                openEmergencyCancellationModal(reservationId);
                                            });
                                        }
                                        
                                        form.parentNode.insertBefore(emergencyBtn, form.nextSibling);
                                    }
                                }
                            }
                        }
                    } else {
                        displayElement.textContent = '{% trans "Error fetching deadline" %}';
                    }
                })
                .catch(error => {
                    console.error('Error fetching cancellation deadline:', error);
                    displayElement.textContent = '{% trans "Error fetching deadline" %}';
                });
        }
        
        // Function to open emergency cancellation modal
        function openEmergencyCancellationModal(reservationId) {
            const modal = document.getElementById('emergencyCancellationModal');
            const reservationIdInput = document.getElementById('emergency_reservation_id');
            
            // Check if button is disabled (meaning a request already exists)
            const btn = document.querySelector(`.emergency-cancel-btn[data-reservation-id="${reservationId}"]`);
            if (btn && btn.disabled) {
                alert('{% trans "You have already submitted an emergency cancellation request for this reservation. You cannot submit multiple requests for the same reservation." %}');
                return;
            }
            
            if (modal && reservationIdInput) {
                reservationIdInput.value = reservationId;
                modal.classList.remove('hidden');
            }
        }
        
        // Function to open cancellation confirmation modal
        function openCancellationConfirmModal(reservationId) {
            const modal = document.getElementById('cancellationConfirmModal');
            const reservationIdInput = document.getElementById('cancellation_reservation_id');
            
            if (modal && reservationIdInput) {
                reservationIdInput.value = reservationId;
                modal.classList.remove('hidden');
            }
        }
        
        // Close emergency cancellation modal
        document.getElementById('closeEmergencyCancellationBtn').addEventListener('click', function() {
            document.getElementById('emergencyCancellationModal').classList.add('hidden');
        });
        
        // Close cancellation confirmation modal
        document.getElementById('closeCancellationBtn').addEventListener('click', function() {
            document.getElementById('cancellationConfirmModal').classList.add('hidden');
        });
        
        // Confirm cancellation and submit form
        document.getElementById('confirmCancellationBtn').addEventListener('click', function() {
            document.getElementById('cancellationForm').submit();
        });
        
        // Submit emergency cancellation form
        document.getElementById('submitEmergencyCancellationBtn').addEventListener('click', function() {
            // Validate reason field
            const reasonField = document.getElementById('reason');
            if (!reasonField.value.trim()) {
                alert('{% trans "Please provide a reason for your cancellation request." %}');
                reasonField.focus();
                return;
            }
            
            // Submit the form
            document.getElementById('emergencyCancellationForm').submit();
        });
        
        // Modify all cancel buttons to use our custom modal
        document.querySelectorAll('button[name="cancel_reservation"]').forEach(button => {
            const form = button.closest('form');
            if (form) {
                form.onsubmit = function(e) {
                    e.preventDefault();
                    const reservationId = form.querySelector('input[name="reservation_id"]').value;
                    openCancellationConfirmModal(reservationId);
                    return false;
                };
            }
        });
        
        // Update all deadline elements
        deadlineElements.forEach(updateDeadline);
        
        // Set up a timer to update deadlines every minute
        setInterval(() => {
            deadlineElements.forEach(updateDeadline);
        }, 60000); // Update every minute

        // Attendance Modal Functions
        const attendanceModal = document.getElementById('attendanceModal');
        const attendanceModalTitle = document.getElementById('attendanceModalTitle');
        const attendanceModalContent = document.getElementById('attendanceModalContent');
        
        // Open attendance modal
        document.querySelectorAll('.view-attendance-btn').forEach(button => {
            button.addEventListener('click', function() {
                const courseInstanceId = this.getAttribute('data-course-instance-id');
                const courseName = this.getAttribute('data-course-name');
                
                // Update modal title
                attendanceModalTitle.innerHTML = `<span class="text-blue-400">${courseName}</span> - {% trans "Attendance" %}`;
                
                // Show loading state
                attendanceModalContent.innerHTML = `
                    <div class="flex items-center justify-center h-32">
                        <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-white"></div>
                    </div>
                `;
                
                // Show modal
                attendanceModal.classList.remove('hidden');
                
                // Fetch attendance data
                fetch(`{% url 'website:user_attendance_api' %}?course_instance_id=${courseInstanceId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.status === 'success') {
                            if (data.sessions.length === 0) {
                                attendanceModalContent.innerHTML = `
                                    <div class="text-center py-10">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                        </svg>
                                        <h3 class="mt-2 text-sm font-medium text-gray-300">{% trans "No Sessions Found" %}</h3>
                                        <p class="mt-1 text-sm text-gray-400">{% trans "There are no sessions associated with this course instance." %}</p>
                                    </div>
                                `;
                                return;
                            }
                            
                            // Create table with sessions
                            let tableHtml = `
                                <div class="overflow-x-auto rounded-lg border border-gray-700" style="max-height: 65vh;">
                                    <table class="min-w-full divide-y divide-gray-700" style="table-layout: fixed;">
                                        <thead class="bg-gray-800">
                                            <tr>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 15%;">
                                                    {% trans "Session ID" %}
                                                </th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 15%;">
                                                    {% trans "Date" %}
                                                </th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 20%;">
                                                    {% trans "Time" %}
                                                </th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 15%;">
                                                    {% trans "Room" %}
                                                </th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 15%;">
                                                    {% trans "Status" %}
                                                </th>
                                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 20%;">
                                                    {% trans "Attendance" %}
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-gray-900 divide-y divide-gray-800">
                            `;
                            
                            data.sessions.forEach(session => {
                                let statusClass, statusLabel;
                                
                                switch(session.status) {
                                    case 'upcoming':
                                        statusClass = 'bg-blue-900/30 text-blue-400 border border-blue-800/50';
                                        statusLabel = '{% trans "Upcoming" %}';
                                        break;
                                    case 'in_progress':
                                        statusClass = 'bg-green-900/30 text-green-400 border border-green-800/50';
                                        statusLabel = '{% trans "In Progress" %}';
                                        break;
                                    case 'completed':
                                        statusClass = 'bg-gray-800/30 text-gray-400 border border-gray-700/50';
                                        statusLabel = '{% trans "Completed" %}';
                                        break;
                                    default:
                                        statusClass = 'bg-gray-800 text-white';
                                        statusLabel = session.status;
                                }
                                
                                let attendanceStatus;
                                if (session.first_half && session.second_half) {
                                    attendanceStatus = `
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/30 text-green-400 border border-green-800/50">
                                            <svg class="mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                            </svg>
                                            {% trans "Attended" %}
                                        </span>
                                    `;
                                } else if (session.first_half || session.second_half) {
                                    attendanceStatus = `
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-900/30 text-yellow-400 border border-yellow-800/50">
                                            <svg class="mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                            </svg>
                                            {% trans "Partial" %}
                                        </span>
                                    `;
                                } else {
                                    if (session.status === 'completed') {
                                        attendanceStatus = `
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-900/30 text-red-400 border border-red-800/50">
                                                <svg class="mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                                {% trans "Absent" %}
                                            </span>
                                        `;
                                    } else {
                                        attendanceStatus = `
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-800/50 text-gray-400 border border-gray-700/50">
                                                {% trans "Not Started" %}
                                            </span>
                                        `;
                                    }
                                }
                                
                                tableHtml += `
                                    <tr class="hover:bg-gray-800/50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            ${session.session_id}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                            ${session.date}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                            ${session.time}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                            ${session.room}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-medium rounded-full ${statusClass}">
                                                ${statusLabel}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            ${attendanceStatus}
                                        </td>
                                    </tr>
                                `;
                            });
                            
                            tableHtml += `
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-5 bg-blue-900/20 border border-blue-800/50 rounded-md p-4 text-sm text-blue-300">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="font-medium text-blue-400 mb-1">{% trans "Attendance Information" %}</p>
                                            <p>{% trans "Attendance is marked by trainers during each session. If you believe there's an error in your attendance record, please contact administration." %}</p>
                                            <p class="mt-2">{% trans "Attendance status key:" %}</p>
                                            <ul class="list-disc list-inside mt-1 ml-2">
                                                <li>{% trans "<strong>Attended</strong>: You were present for the entire session (both first and second half)." %}</li>
                                                <li>{% trans "<strong>Partial</strong>: You were present for only part of the session (either first or second half)." %}</li>
                                                <li>{% trans "<strong>Absent</strong>: You were not present for any part of the session." %}</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            `;
                            
                            attendanceModalContent.innerHTML = tableHtml;
                        } else {
                            attendanceModalContent.innerHTML = `
                                <div class="text-center py-10">
                                    <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-red-300">{% trans "Error" %}</h3>
                                    <p class="mt-1 text-sm text-gray-400">${data.message || '{% trans "An error occurred while fetching attendance data." %}'}</p>
                                </div>
                            `;
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching attendance data:', error);
                        attendanceModalContent.innerHTML = `
                            <div class="text-center py-10">
                                <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-red-300">{% trans "Error" %}</h3>
                                <p class="mt-1 text-sm text-gray-400">{% trans "An error occurred while fetching attendance data." %}</p>
                            </div>
                        `;
                    });
            });
        });
        
        // Close attendance modal
        document.getElementById('closeAttendanceModal').addEventListener('click', function() {
            attendanceModal.classList.add('hidden');
        });

        {% if not request.user.company_name %}
        // Only fetch these for individual users, not corporate users
        fetchIndividualPendingSurveys();
        fetchIndividualPendingQuestionnaires();
        {% endif %}
    });
</script>
{% endblock %} 