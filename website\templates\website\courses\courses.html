{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Courses" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Keep only Request Course button in header if available -->
    {% if is_corporate_admin %}
    <div class="flex justify-end items-center">
        <a href="{% url 'website:request_new_course' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"/>
            </svg>
            {% trans "Request Course Not On System" %}
        </a>
    </div>
    {% endif %}
    <!-- Filters and Search - Single Row (Directly under header) -->
    <div class="bg-white/5 backdrop-blur-md rounded-lg p-6 mb-8">
        <div class="flex flex-wrap items-center justify-between gap-4">
            <!-- Left side: Title -->
            <h1 class="text-2xl font-bold text-white flex items-center">
                <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                </svg>
                {% trans "Courses" %}
            </h1>

            <!-- Center: Filters -->
            <div class="flex flex-wrap items-center gap-4">
                <!-- Search -->
                <div class="min-w-[400px]">
                    <div class="relative">
                        <input type="text" id="search-input" class="block w-full bg-white/5 border border-white/10 rounded-md py-2 pl-10 pr-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="{% trans 'Search courses...' %}">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-4 w-4 text-white/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Category Filter -->
                <div class="flex items-center gap-2">
                    <span class="text-white/70 text-sm flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                        </svg>
                        {% trans "Category:" %}
                    </span>
                    <select id="category-filter" class="bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">{% trans "All Categories" %}</option>
                        <option value="AUTOMOTIVE">{% trans "Automotive" %}</option>
                        <option value="BUSINESS">{% trans "Business Skills" %}</option>
                        <option value="TECHNICAL">{% trans "Technical" %}</option>
                        <option value="PROFESSIONAL">{% trans "Professional Development" %}</option>
                    </select>
                </div>

                <!-- Type Filter -->
                <div class="flex items-center gap-2">
                    <span class="text-white/70 text-sm flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"/>
                        </svg>
                        {% trans "Type:" %}
                    </span>
                    <select id="type-filter" class="bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">{% trans "All Types" %}</option>
                        <option value="ONLINE">{% trans "Online" %}</option>
                        <option value="PHYSICAL">{% trans "Physical" %}</option>
                        <option value="HYBRID">{% trans "Hybrid" %}</option>
                    </select>
                </div>

                <!-- Level Filter -->
                <div class="flex items-center gap-2">
                    <span class="text-white/70 text-sm flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                        {% trans "Level:" %}
                    </span>
                    <select id="level-filter" class="bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">{% trans "All Levels" %}</option>
                        <option value="BEGINNER">{% trans "Beginner" %}</option>
                        <option value="INTERMEDIATE">{% trans "Intermediate" %}</option>
                        <option value="ADVANCED">{% trans "Advanced" %}</option>
                    </select>
                </div>
            </div>

            <!-- Right side: Action Buttons and View Toggle -->
            <div class="flex items-center gap-4">
                <!-- Action Buttons -->
                <div class="flex items-center gap-3">
                    {% if is_trainer or request.user.user_type.code == 'SUPER_ADMIN' %}
                    <a href="{% url 'website:create_course' %}" class="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        {% trans "Create Course" %}
                    </a>
                    {% endif %}
                    {% if is_supervisor or request.user.user_type.code == 'SUPER_ADMIN' %}
                    <a href="{% url 'website:assign_course_to_trainers' %}" class="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary hover:bg-secondary/90">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                        </svg>
                        {% trans "Assign Course" %}
                    </a>
                    {% endif %}
                </div>

                <!-- View Toggle -->
                <div class="flex bg-white/5 rounded-md p-1">
                    <button class="group relative p-2 rounded text-white bg-white/10 view-toggle active" data-view="grid" title="{% trans 'Grid View' %}">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                        </svg>
                    </button>
                    <button class="group relative p-2 rounded text-white/70 hover:text-white hover:bg-white/10 view-toggle" data-view="table" title="{% trans 'Table View' %}">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Grid View -->
    <div id="grid-view" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for category, courses in course_categories.items %}
            <!-- Category Header with Icon -->
            <div class="col-span-full">
                <h2 class="text-xl font-semibold text-white mb-4 flex items-center">
                    <!-- Category Icon -->
                    {% if category == 'Automotive' %}
                        <svg class="w-6 h-6 mr-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                        </svg>
                    {% elif category == 'Business Skills' %}
                        <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                        </svg>
                    {% elif category == 'Technical' %}
                        <svg class="w-6 h-6 mr-3 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                        </svg>
                    {% elif category == 'Professional Development' %}
                        <svg class="w-6 h-6 mr-3 text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                        </svg>
                    {% else %}
                        <svg class="w-6 h-6 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                        </svg>
                    {% endif %}
                    {{ category }}
                </h2>
            </div>

            {% for course in courses %}
            <div class="bg-white/5 backdrop-blur-md rounded-lg overflow-hidden border border-white/10 hover:border-primary transition-colors">
                <div class="relative">
                    <div class="w-full h-48 bg-white/5 flex items-center justify-center overflow-hidden">
                        {% if course.image %}
                            <img src="{{ course.image.url }}" alt="{{ course.name_en }}" class="w-full h-full object-cover">
                        {% elif course.icon_svg %}
                            {{ course.icon_svg|safe }}
                        {% else %}
                            <svg class="w-24 h-24 text-white/20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                            </svg>
                        {% endif %}
                    </div>
                    <div class="absolute top-4 right-4 bg-primary/90 text-white text-xs font-semibold px-2 py-1 rounded">
                        {{ course.get_category_display }}
                    </div>
                </div>
                <div class="p-6">
                    <!-- Category Icon and Title -->
                    <div class="flex items-center mb-3">
                        <!-- Category Icon -->
                        <div class="flex-shrink-0 mr-3">
                            {% if course.category == 'AUTOMOTIVE' %}
                                <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                </svg>
                            {% elif course.category == 'BUSINESS' %}
                                <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                </svg>
                            {% elif course.category == 'TECHNICAL' %}
                                <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                                </svg>
                            {% elif course.category == 'PROFESSIONAL' %}
                                <svg class="w-6 h-6 text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                </svg>
                            {% else %}
                                <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                </svg>
                            {% endif %}
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-white">{{ course.name_en }}</h3>
                        </div>
                    </div>

                    <!-- Separator -->
                    <div class="border-t border-white/10 mb-4"></div>

                    <p class="text-white/70 text-sm mb-4">{{ course.description_en|truncatewords:20 }}</p>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center text-white/70 text-sm">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            {{ course.get_location_display }}
                        </div>
                        {% if is_external_individual or is_gb_user or is_corporate_trainee or is_trainer %}
                        <div class="flex items-center text-white/70 text-sm">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                            </svg>
                            {{ course.instance_count }} {% trans "Available Instances" %}
                        </div>
                        {% endif %}
                    </div>

                    <!-- Action Buttons -->
                    <div class="mt-6 flex justify-between items-center">
                        <a href="{% url 'website:course_detail' course.course_id %}" class="inline-flex items-center px-4 py-2 border border-white/20 rounded-md shadow-sm text-sm font-medium text-white bg-white/10 hover:bg-white/20 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            </svg>
                            {% trans "View Details" %}
                        </a>
                        {% if course.instances %}
                            {% if is_external_individual or is_gb_user or is_corporate_trainee or is_trainer %}
                                <!-- Check if user is enrolled in this course -->
                                {% with user_reservation=None %}
                                    {% for reservation in user_reservations %}
                                        {% if reservation.course_instance.course.course_id == course.course_id %}
                                            {% if not user_reservation %}
                                                <!-- Set the first matching reservation -->
                                                {% comment %}{{ user_reservation := reservation }}{% endcomment %}
                                            {% endif %}
                                        {% endif %}
                                    {% endfor %}
                                {% endwith %}

                                <!-- Check for any enrollment -->
                                {% for reservation in user_reservations %}
                                    {% if reservation.course_instance.course.course_id == course.course_id %}
                                        <!-- Found enrollment! -->
                                        <div class="space-y-3">
                                            <!-- Enrollment Status Card -->
                                            <div class="bg-white/10 border border-white/20 rounded-lg p-3 mb-3">
                                                <div class="flex items-center justify-between mb-2">
                                                    <h4 class="text-sm font-medium text-white">{% trans "Your Enrollment" %}</h4>
                                                    <span class="px-2 py-1 rounded-full text-xs font-medium
                                                        {% if reservation.status == 'WAITING_TO_PAY' %}bg-amber-500/20 text-amber-300{% elif reservation.status == 'UPCOMING' %}bg-green-500/20 text-green-300{% elif reservation.status == 'WAITING_LIST' %}bg-blue-500/20 text-blue-300{% elif reservation.status == 'IN_PROGRESS' %}bg-purple-500/20 text-purple-300{% elif reservation.status == 'COMPLETED' %}bg-gray-500/20 text-gray-300{% endif %}">
                                                        {{ reservation.get_status_display }}
                                                    </span>
                                                </div>

                                                <!-- Enrollment Details -->
                                                <div class="space-y-1 text-xs text-white/70">
                                                    <div class="flex justify-between">
                                                        <span>{% trans "Enrolled:" %}</span>
                                                        <span>{{ reservation.created_at|date:"M d, Y" }}</span>
                                                    </div>
                                                    {% if reservation.course_instance %}
                                                    <div class="flex justify-between">
                                                        <span>{% trans "Starts:" %}</span>
                                                        <span>{{ reservation.course_instance.start_date|date:"M d, Y - H:i" }}</span>
                                                    </div>
                                                    <div class="flex justify-between">
                                                        <span>{% trans "Ends:" %}</span>
                                                        <span>{{ reservation.course_instance.end_date|date:"M d, Y - H:i" }}</span>
                                                    </div>
                                                    <div class="flex justify-between">
                                                        <span>{% trans "Instance:" %}</span>
                                                        <span>{{ reservation.course_instance.instance_id }}</span>
                                                    </div>
                                                {% endif %}
                                                </div>
                                            </div>

                                            <!-- Action Buttons -->
                                            <div class="flex space-x-2">
                                                <!-- Pay Now Button (if waiting to pay) -->
                                                {% if reservation.status == 'WAITING_TO_PAY' %}
                                                <form method="POST" action="{% url 'website:check_before_payment' reservation.reservation_id %}" class="flex-1">
                                                    {% csrf_token %}
                                                    <button type="submit" class="w-full inline-flex items-center justify-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 transition-colors">
                                                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                                                        </svg>
                                                        {% trans "Pay Now" %}
                                                    </button>
                                                </form>
                                                {% elif reservation.status == 'UPCOMING' %}
                                                <div class="flex-1 inline-flex items-center justify-center px-3 py-2 border border-green-500/30 rounded-md text-sm font-medium text-green-300 bg-green-500/10">
                                                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                    </svg>
                                                    {% trans "Paid & Confirmed" %}
                                                </div>
                                                {% elif reservation.status == 'WAITING_LIST' %}
                                                <div class="flex-1 inline-flex items-center justify-center px-3 py-2 border border-blue-500/30 rounded-md text-sm font-medium text-blue-300 bg-blue-500/10">
                                                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                    </svg>
                                                    {% trans "On Waiting List" %}
                                                </div>
                                                {% elif reservation.status == 'IN_PROGRESS' %}
                                                <div class="flex-1 inline-flex items-center justify-center px-3 py-2 border border-purple-500/30 rounded-md text-sm font-medium text-purple-300 bg-purple-500/10">
                                                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.5a2.5 2.5 0 110 5H9m4.5-5H15a2.5 2.5 0 110 5H13.5M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1m-6 0V4a2 2 0 012-2h2a2 2 0 012 2v5"/>
                                                    </svg>
                                                    {% trans "In Progress" %}
                                                </div>
                                                {% elif reservation.status == 'COMPLETED' %}
                                                <div class="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-500/30 rounded-md text-sm font-medium text-gray-300 bg-gray-500/10">
                                                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                    </svg>
                                                    {% trans "Completed" %}
                                                </div>
                                                {% endif %}

                                                <!-- Cancel Button (only for cancellable statuses) -->
                                                {% if reservation.status == 'UPCOMING' or reservation.status == 'WAITING_TO_PAY' or reservation.status == 'WAITING_LIST' %}
                                                <button onclick="openCancellationModal('{{ reservation.reservation_id }}')" class="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors">
                                                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                                    </svg>
                                                    {% trans "Cancel" %}
                                                </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <!-- Don't show enroll button - user is enrolled -->
                                    {% elif forloop.last %}
                                        <!-- Only show enroll button if we've checked all reservations and found no match -->
                                        <button onclick="showEnrollModal('{{ course.course_id }}')" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 transition-colors">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                                            </svg>
                                            {% trans "Enroll" %}
                                        </button>
                                    {% endif %}
                                {% empty %}
                                    <!-- No reservations at all - show enroll button -->
                                    <button onclick="showEnrollModal('{{ course.course_id }}')" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 transition-colors">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                                        </svg>
                                        {% trans "Enroll" %}
                                    </button>
                                {% endfor %}
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        {% empty %}
            <div class="col-span-full text-center text-white/70 py-8">
                {% trans "No courses available." %}
            </div>
        {% endfor %}
    </div>

    <!-- Table View (Hidden by Default) -->
    <div id="table-view" class="hidden">
        <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-white/10">
                    <thead class="bg-white/5">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% trans "Course" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% trans "Category" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% trans "Location" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% if is_external_individual or is_gb_user %}
                                {% trans "Enrollment Status" %}
                                {% else %}
                                {% trans "Status" %}
                                {% endif %}
                            </th>
                            <th scope="col" class="relative px-6 py-3">
                                <span class="sr-only">{% trans "Actions" %}</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-white/10">
                        {% for category, courses in course_categories.items %}
                            {% for course in courses %}
                            <tr class="hover:bg-white/10">
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 bg-white/5 rounded-lg flex items-center justify-center">
                                            {% if course.icon_svg %}
                                                {{ course.icon_svg|safe }}
                                            {% else %}
                                                <svg class="h-6 w-6 text-white/20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                                </svg>
                                            {% endif %}
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-white">{{ course.name_en }}</div>
                                            <div class="text-sm text-white/70">{{ course.description_en|truncatechars:50 }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <!-- Category Icon -->
                                        <div class="flex-shrink-0 mr-2">
                                            {% if course.category == 'AUTOMOTIVE' %}
                                                <svg class="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                                </svg>
                                            {% elif course.category == 'BUSINESS' %}
                                                <svg class="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                                </svg>
                                            {% elif course.category == 'TECHNICAL' %}
                                                <svg class="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                                                </svg>
                                            {% elif course.category == 'PROFESSIONAL' %}
                                                <svg class="w-4 h-4 text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                                </svg>
                                            {% else %}
                                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                                </svg>
                                            {% endif %}
                                        </div>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-primary/30 text-white">
                                            {{ course.get_category_display }}
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white/70">
                                    {{ course.get_location_display }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white/70">
                                    {% if is_external_individual or is_gb_user or is_corporate_trainee or is_trainer %}
                                        <!-- Check for enrollment -->
                                        {% with enrolled_reservation=None %}
                                            {% for reservation in user_reservations %}
                                                {% if reservation.course_instance.course.course_id == course.course_id and not enrolled_reservation %}
                                                    <div class="flex items-center space-x-2">
                                                        <span class="px-2 py-1 rounded-full text-xs font-medium
                                                            {% if reservation.status == 'WAITING_TO_PAY' %}bg-amber-500/20 text-amber-300{% elif reservation.status == 'UPCOMING' %}bg-green-500/20 text-green-300{% elif reservation.status == 'WAITING_LIST' %}bg-blue-500/20 text-blue-300{% elif reservation.status == 'IN_PROGRESS' %}bg-purple-500/20 text-purple-300{% elif reservation.status == 'COMPLETED' %}bg-gray-500/20 text-gray-300{% endif %}">
                                                            {{ reservation.get_status_display }}
                                                        </span>
                                                    </div>
                                                    <div class="text-xs text-white/50 mt-1">
                                                        {% if reservation.course_instance %}
                                                            {{ reservation.course_instance.start_date|date:"M d, Y" }} - {{ reservation.course_instance.end_date|date:"M d, Y" }}
                                                        {% endif %}
                                                    </div>
                                                    {% with enrolled_reservation=reservation %}{% endwith %}
                                                {% endif %}
                                            {% endfor %}
                                            
                                            {% if not enrolled_reservation %}
                                        {{ course.instance_count }} {% trans "Available" %}
                                            {% endif %}
                                        {% endwith %}
                                    {% else %}
                                        --
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex justify-end space-x-2">
                                        <a href="{% url 'website:course_detail' course.course_id %}" class="inline-flex items-center px-3 py-1.5 border border-white/20 rounded-md shadow-sm text-xs font-medium text-white bg-white/10 hover:bg-white/20 transition-colors">
                                            <svg class="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                            </svg>
                                            {% trans "View Details" %}
                                        </a>
                                        {% if course.instances %}
                                            {% if is_external_individual or is_gb_user or is_corporate_trainee or is_trainer %}
                                                <!-- Check for any enrollment -->
                                                {% for reservation in user_reservations %}
                                                    {% if reservation.course_instance.course.course_id == course.course_id %}
                                                        <!-- Show enrolled user actions -->
                                                        <div class="flex space-x-1">
                                                            <!-- Pay Now Button (if waiting to pay) -->
                                                            {% if reservation.status == 'WAITING_TO_PAY' %}
                                                            <form method="POST" action="{% url 'website:check_before_payment' reservation.reservation_id %}">
                                                                {% csrf_token %}
                                                                <button type="submit" class="inline-flex items-center px-2 py-1 border border-transparent rounded text-xs font-medium text-white bg-green-600 hover:bg-green-700 transition-colors">
                                                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                                                                    </svg>
                                                                    {% trans "Pay Now" %}
                                                                </button>
                                                            </form>
                                                            {% elif reservation.status == 'UPCOMING' %}
                                                            <div class="inline-flex items-center px-2 py-1 border border-green-500/30 rounded text-xs font-medium text-green-300 bg-green-500/10">
                                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                                </svg>
                                                                {% trans "Paid" %}
                                                            </div>
                                                            {% endif %}

                                                            <!-- Cancel Button (only for cancellable statuses) -->
                                                            {% if reservation.status == 'UPCOMING' or reservation.status == 'WAITING_TO_PAY' or reservation.status == 'WAITING_LIST' %}
                                                            <button onclick="openCancellationModal('{{ reservation.reservation_id }}')" class="inline-flex items-center px-2 py-1 border border-transparent rounded text-xs font-medium text-white bg-red-600 hover:bg-red-700 transition-colors">
                                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                                                </svg>
                                                                {% trans "Cancel" %}
                                                            </button>
                                                            {% endif %}
                                                        </div>
                                                        <!-- Don't show enroll button - user is enrolled -->
                                                    {% elif forloop.last %}
                                                        <!-- Only show enroll button if we've checked all reservations and found no match -->
                                                        <button onclick="showEnrollModal('{{ course.course_id }}')" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-primary hover:bg-primary/90 transition-colors">
                                                            <svg class="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                                                            </svg>
                                                            {% trans "Enroll" %}
                                                        </button>
                                                    {% endif %}
                                                {% empty %}
                                                    <!-- No reservations at all - show enroll button -->
                                                    <button onclick="showEnrollModal('{{ course.course_id }}')" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-primary hover:bg-primary/90 transition-colors">
                                                        <svg class="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                                                        </svg>
                                                        {% trans "Enroll" %}
                                                    </button>
                                                {% endfor %}
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Enroll Modal -->
{% if is_external_individual or is_gb_user or is_corporate_trainee or is_trainer %}
<div id="enroll-modal" class="fixed inset-0 bg-black/50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-xl font-bold text-white mb-4">{% trans "Choose Course Instance" %}</h3>
        <div class="mb-6">
            <select id="instance-select" class="w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                <option value="" class="bg-gray-800">{% trans "Select an instance" %}</option>
            </select>
        </div>
        <div class="flex justify-end space-x-3">
            <button onclick="closeEnrollModal()" class="px-4 py-2 text-white/70 hover:text-white">
                {% trans "Cancel" %}
            </button>
            <button onclick="enrollInInstance()" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                {% trans "Enroll" %}
            </button>
        </div>
    </div>
</div>

<script>
let enrollUrl = '';
{% if is_gb_user %}
    enrollUrl = "{% url 'website:gb_enroll_in_instance' 999999 %}";
{% elif is_external_individual %}
    enrollUrl = "{% url 'website:enroll_in_instance' 999999 %}";
{% endif %}

let selectedInstanceId = null;
let courseIdForModal = null;
let courseInstances = {};

{% for category, courses in course_categories.items %}
    {% for course in courses %}
        courseInstances['{{ course.course_id }}'] = [
            {% for instance in course.instances %}
            {
                id: '{{ instance.instance_id }}',
                startDate: '{{ instance.start_date|date:"M d, Y" }}',
                startDateRaw: '{{ instance.start_date|date:"Y-m-d" }}',
                endDate: '{{ instance.end_date|date:"M d, Y" }}',
                sessionCount: {{ instance.sessions.count }},
                capacity: {{ instance.capacity }},
                availableSeats: {{ instance.available_seats }},
                waitingListCount: {{ instance.waiting_list_count }},
                courseStarted: {% if instance.time_until_deadline == "Deadline has passed" %}true{% else %}false{% endif %}
            },
            {% endfor %}
        ];
    {% endfor %}
{% endfor %}

// Track user enrollments
let userEnrollments = [
    {% for reservation in user_reservations %}
        {% if reservation.course_instance %}
            "{{ reservation.course_instance.instance_id }}",
        {% endif %}
    {% endfor %}
];

// Check if start date has passed
function hasStartDatePassed(startDateRaw) {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to beginning of day
    const startDate = new Date(startDateRaw);
    return today >= startDate;
}

function showEnrollModal(courseId) {
    const instances = courseInstances[courseId];
    const instanceSelect = document.getElementById('instance-select');
    instanceSelect.innerHTML = '<option value="" class="bg-gray-800">{% trans "Select an instance" %}</option>';

    instances.forEach(instance => {
        // Check if user is already enrolled (and not cancelled)
        const isEnrolled = userEnrollments.includes(instance.id);
        const startDatePassed = hasStartDatePassed(instance.startDateRaw);
        
        // Skip instances where user is already enrolled or start date has passed
        if (isEnrolled || startDatePassed) {
            return; // Don't add this instance to the dropdown
        }
        
        const option = document.createElement('option');
        option.value = instance.id;
        option.className = 'bg-gray-800';
        
        if (instance.availableSeats <= 0) {
            option.textContent = `${instance.startDate} - ${instance.endDate} (${instance.sessionCount} {% trans "Sessions" %}, ${instance.capacity} {% trans "seats" %}, {% trans "Fully Booked" %})`;
            if (instance.waitingListCount > 0) {
                option.textContent += ` - {% trans "Waiting List" %}: ${instance.waitingListCount}`;
            }
        } else {
            option.textContent = `${instance.startDate} - ${instance.endDate} (${instance.sessionCount} {% trans "Sessions" %}, ${instance.capacity} {% trans "seats" %}, ${instance.availableSeats} {% trans "seats left" %})`;
        }
        instanceSelect.appendChild(option);
    });

    // Reset selection
    selectedInstanceId = null;
    instanceSelect.value = '';

    // Add change event listener
    instanceSelect.addEventListener('change', (e) => {
        selectedInstanceId = e.target.value;
    });

    document.getElementById('enroll-modal').classList.remove('hidden');
    document.getElementById('enroll-modal').classList.add('flex');
}

// Function to show notification in the modal
function showModalNotification(message, type) {
    // Remove any existing notification
    const existingNotification = document.getElementById('modal-notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // Create new notification
    const notification = document.createElement('div');
    notification.id = 'modal-notification';
    
    // Set styles based on type
    if (type === 'error') {
        notification.className = 'bg-red-500/10 border-red-500/30 text-red-400 p-3 rounded-md border mb-4';
    } else if (type === 'warning') {
        notification.className = 'bg-amber-500/10 border-amber-500/30 text-amber-400 p-3 rounded-md border mb-4';
    } else if (type === 'success') {
        notification.className = 'bg-green-500/10 border-green-500/30 text-green-400 p-3 rounded-md border mb-4';
    }
    
    notification.textContent = message;
    
    // Insert at the top of the modal content
    const modalContent = document.querySelector('#enroll-modal > div');
    modalContent.insertBefore(notification, modalContent.firstChild);
    
    // Set timeout based on notification type
    let timeout = 5000; // Default: 5 seconds
    
    if (type === 'success') {
        timeout = 5000; // 5 seconds for success
    } else if (type === 'warning') {
        timeout = 7000; // 7 seconds for warnings
    } else if (type === 'error') {
        timeout = 10000; // 10 seconds for errors
    }
    
    // Auto-dismiss all notifications after their timeout
    setTimeout(() => {
        notification.style.transition = 'opacity 0.5s ease';
        notification.style.opacity = '0';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 500);
    }, timeout);
}

function closeEnrollModal() {
    document.getElementById('enroll-modal').classList.add('hidden');
    document.getElementById('enroll-modal').classList.remove('flex');
    selectedInstanceId = null;
    
    // Remove any notifications
    const notification = document.getElementById('modal-notification');
    if (notification) {
        notification.remove();
    }
}

function enrollInInstance() {
    if (!selectedInstanceId) {
        showModalNotification('{% trans "Please select an instance" %}', 'warning');
        return;
    }
    
    // Find which course this instance belongs to
    const courseId = Object.keys(courseInstances).find(id => 
        courseInstances[id].some(instance => instance.id === selectedInstanceId)
    );
    
    if (!courseId) {
        showModalNotification('{% trans "Invalid course instance" %}', 'error');
        return;
    }
    
    const instance = courseInstances[courseId].find(instance => instance.id === selectedInstanceId);
    
    // Check if already enrolled
    if (userEnrollments.includes(selectedInstanceId)) {
        showModalNotification('{% trans "You are already enrolled in this course instance" %}', 'warning');
        return;
    }
    
    // Check if start date has passed
    if (instance && hasStartDatePassed(instance.startDateRaw)) {
        showModalNotification('{% trans "Enrollment for this course instance is closed because the course has already started." %}', 'error');
        return;
    }
    
    if (instance && instance.availableSeats <= 0) {
        // Ask if they want to join the waiting list instead
        if (confirm('{% trans "This course instance is fully booked. Would you like to join the waiting list instead?" %}')) {
            // Redirect to enrollment with waitlist parameter
            window.location.href = enrollUrl.replace('999999', selectedInstanceId) + '?waitlist=yes';
            return;
        } else {
            return; // User declined to join waitlist
        }
    }
    
    // All checks passed, proceed with enrollment
    window.location.href = enrollUrl.replace('999999', selectedInstanceId);
}

// Cancellation Modal Functions
function openCancellationModal(reservationId) {
    document.getElementById('cancellation_reservation_id').value = reservationId;
    document.getElementById('cancellationConfirmModal').classList.remove('hidden');
}

function closeCancellationModal() {
    document.getElementById('cancellationConfirmModal').classList.add('hidden');
}
</script>

<!-- Cancellation Confirmation Modal -->
<div id="cancellationConfirmModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-16 px-4 pb-20 text-center">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-black opacity-75"></div>
        </div>
        <div class="inline-block align-bottom bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-white">{% trans "Cancel Reservation" %}</h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-300">{% trans "Are you sure you want to cancel this reservation? This action cannot be undone." %}</p>
                            <p class="mt-2 text-sm text-gray-300">{% trans "Please note that cancellation policies may apply. You can cancel your reservation up to the cancellation deadline without penalty." %}</p>
                            <p class="mt-2 text-sm text-gray-300">{% trans "For example, if the cancellation period is 3 business days, you must cancel at least 3 business days before the course starts. If the course starts on Monday, and the cancellation period is 3 business days, you must cancel by the previous Tuesday (assuming no holidays in between)." %}</p>
                            <p class="mt-2 text-sm text-gray-300">{% trans "If you need to cancel after the deadline has passed, please submit an emergency cancellation request." %}</p>
                        </div>
                        <form id="cancellationForm" method="POST" action="{% url 'website:user_reservations' %}">
                            {% csrf_token %}
                            <input type="hidden" id="cancellation_reservation_id" name="reservation_id" value="">
                            <input type="hidden" name="cancel_reservation" value="true">
                        </form>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" onclick="document.getElementById('cancellationForm').submit();" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Confirm Cancel" %}
                </button>
                <button type="button" onclick="closeCancellationModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Cancel" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block footer %}
    {% include 'website/footer.html' %}
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const gridView = document.getElementById('grid-view');
        const tableView = document.getElementById('table-view');
        const viewToggles = document.querySelectorAll('.view-toggle');
        const searchInput = document.getElementById('search-input');
        const categoryFilter = document.getElementById('category-filter');
        const typeFilter = document.getElementById('type-filter');
        const levelFilter = document.getElementById('level-filter');



        // View toggle functionality
        function switchView(viewType) {
            // Remove active class from all toggles
            viewToggles.forEach(t => {
                t.classList.remove('active', 'bg-white/10', 'text-white');
                t.classList.add('text-white/70', 'hover:text-white', 'hover:bg-white/10');
            });

            // Add active class to clicked toggle
            const activeToggle = document.querySelector(`[data-view="${viewType}"]`);
            if (activeToggle) {
                activeToggle.classList.add('active', 'bg-white/10', 'text-white');
                activeToggle.classList.remove('text-white/70', 'hover:text-white', 'hover:bg-white/10');
            }

            // Show selected view
            if (viewType === 'grid') {
                gridView.classList.remove('hidden');
                tableView.classList.add('hidden');
            } else if (viewType === 'table') {
                gridView.classList.add('hidden');
                tableView.classList.remove('hidden');
            }
        }

        // Add click listeners to view toggle buttons
        viewToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                const view = this.getAttribute('data-view');
                switchView(view);
            });
        });

        // Filter and search functionality
        function filterCourses() {
            const searchTerm = searchInput.value.toLowerCase();
            const selectedCategory = categoryFilter.value;
            const selectedType = typeFilter.value;
            const selectedLevel = levelFilter.value;

            // Filter grid view cards
            const gridCards = gridView.querySelectorAll('.bg-white\\/5.backdrop-blur-md');
            const categoryHeaders = gridView.querySelectorAll('.col-span-full');

            gridCards.forEach(card => {
                const courseName = card.querySelector('h3').textContent.toLowerCase();
                const courseDescription = card.querySelector('p').textContent.toLowerCase();
                const categoryBadge = card.querySelector('.bg-primary\\/90');
                const locationElement = card.querySelector('.flex.items-center.text-white\\/70');

                let categoryMatch = true;
                let typeMatch = true;
                let levelMatch = true;
                let searchMatch = true;

                // Search match
                if (searchTerm) {
                    searchMatch = courseName.includes(searchTerm) || courseDescription.includes(searchTerm);
                }

                // Category match
                if (selectedCategory && categoryBadge) {
                    const cardCategory = categoryBadge.textContent.trim();
                    categoryMatch = cardCategory.toLowerCase().includes(selectedCategory.toLowerCase());
                }

                // Type match (location)
                if (selectedType && locationElement) {
                    const cardType = locationElement.textContent.toLowerCase();
                    typeMatch = cardType.includes(selectedType.toLowerCase());
                }

                // Show/hide card based on all filters
                if (searchMatch && categoryMatch && typeMatch && levelMatch) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });

            // Hide empty category sections
            categoryHeaders.forEach(header => {
                const categorySection = header.parentElement;
                const visibleCards = Array.from(categorySection.querySelectorAll('.bg-white\\/5.backdrop-blur-md'))
                    .filter(card => card.style.display !== 'none');

                if (visibleCards.length === 0) {
                    header.style.display = 'none';
                } else {
                    header.style.display = 'block';
                }
            });

            // Filter table view rows
            const tableRows = tableView.querySelectorAll('tbody tr');
            tableRows.forEach(row => {
                const courseName = row.querySelector('td:first-child').textContent.toLowerCase();
                const categoryCell = row.querySelector('td:nth-child(2)');
                const locationCell = row.querySelector('td:nth-child(3)');

                let categoryMatch = true;
                let typeMatch = true;
                let levelMatch = true;
                let searchMatch = true;

                // Search match
                if (searchTerm) {
                    searchMatch = courseName.includes(searchTerm);
                }

                // Category match
                if (selectedCategory && categoryCell) {
                    const rowCategory = categoryCell.textContent.trim();
                    categoryMatch = rowCategory.toLowerCase().includes(selectedCategory.toLowerCase());
                }

                // Type match (location)
                if (selectedType && locationCell) {
                    const rowType = locationCell.textContent.toLowerCase();
                    typeMatch = rowType.includes(selectedType.toLowerCase());
                }

                // Show/hide row based on all filters
                if (searchMatch && categoryMatch && typeMatch && levelMatch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // Add event listeners for filters
        searchInput.addEventListener('input', filterCourses);
        categoryFilter.addEventListener('change', filterCourses);
        typeFilter.addEventListener('change', filterCourses);
        levelFilter.addEventListener('change', filterCourses);
    });
</script>
{% endblock %}